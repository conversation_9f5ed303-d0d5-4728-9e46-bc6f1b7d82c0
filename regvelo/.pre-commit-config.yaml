fail_fast: false
default_language_version:
    python: python3
default_stages:
    - commit
    - push
minimum_pre_commit_version: 2.16.0
repos:
    - repo: https://github.com/psf/black
      rev: "23.1.0"
      hooks:
          - id: black
    - repo: https://github.com/asottile/blacken-docs
      rev: 1.13.0
      hooks:
          - id: blacken-docs
    - repo: https://github.com/pre-commit/mirrors-prettier
      rev: v3.0.0-alpha.6
      hooks:
          - id: prettier
            # Newer versions of node don't work on systems that have an older version of GLIBC
            # (in particular Ubuntu 18.04 and Centos 7)
            # EOL of Centos 7 is in 2024-06, we can probably get rid of this then.
            # See https://github.com/scverse/cookiecutter-scverse/issues/143 and
            # https://github.com/jupyterlab/jupyterlab/issues/12675
            language_version: "17.9.1"
    - repo: https://github.com/charliermarsh/ruff-pre-commit
      rev: v0.0.254
      hooks:
          - id: ruff
            args: [--fix, --exit-non-zero-on-fix]
    - repo: https://github.com/pre-commit/pre-commit-hooks
      rev: v4.4.0
      hooks:
          - id: detect-private-key
          - id: check-ast
          - id: end-of-file-fixer
          - id: mixed-line-ending
            args: [--fix=lf]
          - id: trailing-whitespace
          - id: check-case-conflict
    - repo: local
      hooks:
          - id: forbid-to-commit
            name: Don't commit rej files
            entry: |
                Cannot commit .rej files. These indicate merge conflicts that arise during automated template updates.
                Fix the merge conflicts manually and remove the .rej files.
            language: fail
            files: '.*\.rej$'
