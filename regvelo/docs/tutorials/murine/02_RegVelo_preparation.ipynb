{"cells": [{"cell_type": "markdown", "id": "e224f114-2918-4942-82d1-801e1429af7b", "metadata": {}, "source": ["# Preprocess data and add prior GRN information\n", "In this notebook, we will go through the preprocessing steps needed prior to running RegVelo pipeline."]}, {"cell_type": "markdown", "id": "862aad5e-ac38-4ad6-9557-989e8f261fe8", "metadata": {}, "source": ["## Library import "]}, {"cell_type": "code", "execution_count": 1, "id": "b1205fa6-37b9-45b7-bae4-6f45369db6f6", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/yifan.chen/miniconda3/envs/regvelo-py310-v2/lib/python3.10/site-packages/anndata/utils.py:434: FutureWarning: Importing read_csv from `anndata` is deprecated. Import anndata.io.read_csv instead.\n", "  warnings.warn(msg, FutureWarning)\n", "/home/<USER>/yifan.chen/miniconda3/envs/regvelo-py310-v2/lib/python3.10/site-packages/anndata/utils.py:434: FutureWarning: Importing read_excel from `anndata` is deprecated. Import anndata.io.read_excel instead.\n", "  warnings.warn(msg, FutureWarning)\n", "/home/<USER>/yifan.chen/miniconda3/envs/regvelo-py310-v2/lib/python3.10/site-packages/anndata/utils.py:434: FutureWarning: Importing read_hdf from `anndata` is deprecated. Import anndata.io.read_hdf instead.\n", "  warnings.warn(msg, FutureWarning)\n", "/home/<USER>/yifan.chen/miniconda3/envs/regvelo-py310-v2/lib/python3.10/site-packages/anndata/utils.py:434: FutureWarning: Importing read_loom from `anndata` is deprecated. Import anndata.io.read_loom instead.\n", "  warnings.warn(msg, FutureWarning)\n", "/home/<USER>/yifan.chen/miniconda3/envs/regvelo-py310-v2/lib/python3.10/site-packages/anndata/utils.py:434: FutureWarning: Importing read_mtx from `anndata` is deprecated. Import anndata.io.read_mtx instead.\n", "  warnings.warn(msg, FutureWarning)\n", "/home/<USER>/yifan.chen/miniconda3/envs/regvelo-py310-v2/lib/python3.10/site-packages/anndata/utils.py:434: FutureWarning: Importing read_text from `anndata` is deprecated. Import anndata.io.read_text instead.\n", "  warnings.warn(msg, FutureWarning)\n", "/home/<USER>/yifan.chen/miniconda3/envs/regvelo-py310-v2/lib/python3.10/site-packages/anndata/utils.py:434: FutureWarning: Importing read_umi_tools from `anndata` is deprecated. Import anndata.io.read_umi_tools instead.\n", "  warnings.warn(msg, FutureWarning)\n"]}], "source": ["import scvelo as scv\n", "import scanpy as sc\n", "import pandas as pd\n", "import numpy as np\n", "\n", "import matplotlib.pyplot as plt\n", "import mplscience\n", "import seaborn as sns\n", "\n", "import regvelo as rgv"]}, {"cell_type": "markdown", "id": "cd088464-c366-40a8-a34e-8d3947fa4991", "metadata": {}, "source": ["## Load data\n", "Read murine neural crest data that contains `.layers['spliced']` and `.layers['unspliced']`."]}, {"cell_type": "code", "execution_count": null, "id": "2c1022b2-6133-4072-acb1-df900c0875b7", "metadata": {}, "outputs": [], "source": ["adata = rgv.datasets.murine_nc(data_type = \"velocyto\")"]}, {"cell_type": "code", "execution_count": 3, "id": "cf07867d-5b7a-4feb-82e1-fa382ad19613", "metadata": {}, "outputs": [{"data": {"text/plain": ["AnnData object with n_obs × n_vars = 6788 × 30717\n", "    obs: 'nCount_RNA', 'nFeature_RNA', 'cell_id', 'UMI_count', 'gene_count', 'major_trajectory', 'celltype_update', 'UMAP_1', 'UMAP_2', 'UMAP_3', 'UMAP_2d_1', 'UMAP_2d_2', 'terminal_state', 'nCount_intron', 'nFeature_intron'\n", "    var: 'vf_vst_counts_mean', 'vf_vst_counts_variance', 'vf_vst_counts_variance.expected', 'vf_vst_counts_variance.standardized', 'vf_vst_counts_variable', 'vf_vst_counts_rank', 'var.features', 'var.features.rank'\n", "    obsm: 'X_pca', 'X_umap'\n", "    layers: 'spliced', 'unspliced'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["adata"]}, {"cell_type": "markdown", "id": "aa1e8c0d-7e11-4a77-bd6f-042e27a09b6e", "metadata": {}, "source": ["## Data preprocessing"]}, {"cell_type": "code", "execution_count": 4, "id": "85fa5243-6477-4a86-9640-4fd2f1172d1d", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Filtered out 22217 genes that are detected 20 counts (shared).\n", "Normalized count data: X, spliced, unspliced.\n", "Extracted 3000 highly variable genes.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3655946/1257038519.py:4: DeprecationWarning: `log1p` is deprecated since scVelo v0.3.0 and will be removed in a future version. Please use `log1p` from `scanpy.pp` instead.\n", "  scv.pp.log1p(adata)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["computing moments based on connectivities\n", "    finished (0:00:02) --> added \n", "    'Ms' and 'Mu', moments of un/spliced abundances (adata.layers)\n"]}], "source": ["scv.pp.filter_genes(adata, min_shared_counts=20)\n", "scv.pp.normalize_per_cell(adata)\n", "scv.pp.filter_genes_dispersion(adata, n_top_genes=3000)\n", "scv.pp.log1p(adata)\n", "\n", "sc.pp.neighbors(adata,n_pcs = 30,n_neighbors = 50)\n", "sc.tl.umap(adata)\n", "scv.pp.moments(adata, n_pcs=None, n_neighbors=None)"]}, {"cell_type": "code", "execution_count": 5, "id": "91c087a7-ec36-4d86-a891-c297eba29a7c", "metadata": {}, "outputs": [{"data": {"text/plain": ["AnnData object with n_obs × n_vars = 6788 × 3000\n", "    obs: 'nCount_RNA', 'nFeature_RNA', 'cell_id', 'UMI_count', 'gene_count', 'major_trajectory', 'celltype_update', 'UMAP_1', 'UMAP_2', 'UMAP_3', 'UMAP_2d_1', 'UMAP_2d_2', 'terminal_state', 'nCount_intron', 'nFeature_intron', 'initial_size_unspliced', 'initial_size_spliced', 'initial_size', 'n_counts'\n", "    var: 'vf_vst_counts_mean', 'vf_vst_counts_variance', 'vf_vst_counts_variance.expected', 'vf_vst_counts_variance.standardized', 'vf_vst_counts_variable', 'vf_vst_counts_rank', 'var.features', 'var.features.rank', 'gene_count_corr', 'means', 'dispersions', 'dispersions_norm', 'highly_variable'\n", "    uns: 'log1p', 'neighbors', 'umap'\n", "    obsm: 'X_pca', 'X_umap'\n", "    layers: 'spliced', 'unspliced', 'Ms', 'Mu'\n", "    obsp: 'distances', 'connectivities'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["adata"]}, {"cell_type": "markdown", "id": "943f6605-e091-4b8c-ad4b-075dbc4ef803", "metadata": {}, "source": ["## Load prior GRN created from notebook 'Infer prior GRN from [pySCENIC](https://pyscenic.readthedocs.io/en/latest/installation.html)' for RegVelo\n", "In the following, we load the processed prior GRN infromation into our AnnData object. In this step `.uns['skeleton']` and `.var['TF']` are added, which will be needed for RegVelo's velocity pipeline."]}, {"cell_type": "code", "execution_count": 6, "id": "f7d9c59f-6238-494b-8649-178ae5dd783b", "metadata": {}, "outputs": [], "source": ["GRN = pd.read_parquet(\"regulon_mat_processed_all_regulons.parquet\")"]}, {"cell_type": "code", "execution_count": 7, "id": "63410f40-8ee1-48cb-9a93-5737fb3aa9ef", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0610005C13Rik</th>\n", "      <th>0610009L18Rik</th>\n", "      <th>0610010K14Rik</th>\n", "      <th>0610012G03Rik</th>\n", "      <th>0610030E20Rik</th>\n", "      <th>0610038B21Rik</th>\n", "      <th>0610040B10Rik</th>\n", "      <th>0610040J01Rik</th>\n", "      <th>0610043K17Rik</th>\n", "      <th>1110002L01Rik</th>\n", "      <th>...</th>\n", "      <th>Zswim8</th>\n", "      <th>Zw10</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>Zwint</th>\n", "      <th>Zxdb</th>\n", "      <th>Zxdc</th>\n", "      <th>Zyg11b</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>Zzef1</th>\n", "      <th>Zzz3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0610005C13Rik</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0610009L18Rik</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0610010K14Rik</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0610012G03Rik</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0610030E20Rik</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 13697 columns</p>\n", "</div>"], "text/plain": ["               0610005C13Rik  0610009L18Rik  0610010K14Rik  0610012G03Rik  \\\n", "0610005C13Rik              0              0              0              0   \n", "0610009L18Rik              0              0              0              0   \n", "0610010K14Rik              0              0              0              0   \n", "0610012G03Rik              0              0              0              0   \n", "0610030E20Rik              0              0              0              0   \n", "\n", "               0610030E20Rik  0610038B21Rik  0610040B10Rik  0610040J01Rik  \\\n", "0610005C13Rik              0              0              0              0   \n", "0610009L18Rik              0              0              0              0   \n", "0610010K14Rik              0              0              0              0   \n", "0610012G03Rik              0              0              0              0   \n", "0610030E20Rik              0              0              0              0   \n", "\n", "               0610043K17Rik  1110002L01Rik  ...  Zswim8  Zw10  Zwilch  Zwint  \\\n", "0610005C13Rik              0              0  ...       0     0       0      0   \n", "0610009L18Rik              0              0  ...       0     0       0      0   \n", "0610010K14Rik              0              0  ...       0     0       0      0   \n", "0610012G03Rik              0              0  ...       0     0       0      0   \n", "0610030E20Rik              0              0  ...       0     0       0      0   \n", "\n", "               Zxdb  Zxdc  Zyg11b  Zyx  Zzef1  Zzz3  \n", "0610005C13Rik     0     0       0    0      0     0  \n", "0610009L18Rik     0     0       0    0      0     0  \n", "0610010K14Rik     0     0       0    0      0     0  \n", "0610012G03Rik     0     0       0    0      0     0  \n", "0610030E20Rik     0     0       0    0      0     0  \n", "\n", "[5 rows x 13697 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["GRN.head()"]}, {"cell_type": "code", "execution_count": 8, "id": "2087dc84-d2cd-4ae1-989f-5691ac6dbbc0", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/yifan.chen/miniconda3/envs/regvelo-py310-v2/lib/python3.10/site-packages/regvelo/preprocessing/set_prior_grn.py:75: ImplicitModificationWarning: Trying to modify attribute `._uns` of view, initializing view as actual.\n", "  adata.uns[\"regulators\"] = adata.var_names.to_numpy()\n"]}], "source": ["adata = rgv.pp.set_prior_grn(adata, GRN.T)"]}, {"cell_type": "code", "execution_count": 9, "id": "652d5597-1abd-4c71-a328-00d3d8f5510f", "metadata": {}, "outputs": [{"data": {"text/plain": ["AnnData object with n_obs × n_vars = 6788 × 2112\n", "    obs: 'nCount_RNA', 'nFeature_RNA', 'cell_id', 'UMI_count', 'gene_count', 'major_trajectory', 'celltype_update', 'UMAP_1', 'UMAP_2', 'UMAP_3', 'UMAP_2d_1', 'UMAP_2d_2', 'terminal_state', 'nCount_intron', 'nFeature_intron', 'initial_size_unspliced', 'initial_size_spliced', 'initial_size', 'n_counts'\n", "    var: 'vf_vst_counts_mean', 'vf_vst_counts_variance', 'vf_vst_counts_variance.expected', 'vf_vst_counts_variance.standardized', 'vf_vst_counts_variable', 'vf_vst_counts_rank', 'var.features', 'var.features.rank', 'gene_count_corr', 'means', 'dispersions', 'dispersions_norm', 'highly_variable'\n", "    uns: 'log1p', 'neighbors', 'umap', 'regulators', 'targets', 'skeleton', 'network'\n", "    obsm: 'X_pca', 'X_umap'\n", "    layers: 'spliced', 'unspliced', 'Ms', 'Mu'\n", "    obsp: 'distances', 'connectivities'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["adata"]}, {"cell_type": "code", "execution_count": 10, "id": "36ccbcad-1c0e-4807-9fd0-3ba512b8ced3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["computing velocities\n", "    finished (0:00:00) --> added \n", "    'velocity', velocity vectors for each individual cell (adata.layers)\n"]}], "source": ["velocity_genes = rgv.pp.preprocess_data(adata.copy()).var_names.tolist()\n", "\n", "# select TFs that regulate at least one gene\n", "TF = adata.var_names[adata.uns[\"skeleton\"].sum(1) != 0]\n", "var_mask = np.union1d(TF, velocity_genes)\n", "\n", "adata = adata[:, var_mask].copy()"]}, {"cell_type": "code", "execution_count": 11, "id": "575c89de-7fc5-4a3b-9e8a-0287028f90fe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of genes: 1187\n", "Number of genes: 1164\n"]}], "source": ["adata = rgv.pp.filter_genes(adata)\n", "adata = rgv.pp.preprocess_data(adata, filter_on_r2=False)\n", "\n", "adata.var[\"velocity_genes\"] = adata.var_names.isin(velocity_genes)\n", "adata.var[\"TF\"] = adata.var_names.isin(TF)"]}, {"cell_type": "code", "execution_count": 12, "id": "152b7016-ff2a-435e-9f02-601ceadc31a7", "metadata": {}, "outputs": [{"data": {"text/plain": ["AnnData object with n_obs × n_vars = 6788 × 1164\n", "    obs: 'nCount_RNA', 'nFeature_RNA', 'cell_id', 'UMI_count', 'gene_count', 'major_trajectory', 'celltype_update', 'UMAP_1', 'UMAP_2', 'UMAP_3', 'UMAP_2d_1', 'UMAP_2d_2', 'terminal_state', 'nCount_intron', 'nFeature_intron', 'initial_size_unspliced', 'initial_size_spliced', 'initial_size', 'n_counts'\n", "    var: 'vf_vst_counts_mean', 'vf_vst_counts_variance', 'vf_vst_counts_variance.expected', 'vf_vst_counts_variance.standardized', 'vf_vst_counts_variable', 'vf_vst_counts_rank', 'var.features', 'var.features.rank', 'gene_count_corr', 'means', 'dispersions', 'dispersions_norm', 'highly_variable', 'velocity_genes', 'TF'\n", "    uns: 'log1p', 'neighbors', 'umap', 'regulators', 'targets', 'skeleton', 'network'\n", "    obsm: 'X_pca', 'X_umap'\n", "    layers: 'spliced', 'unspliced', 'Ms', 'Mu'\n", "    obsp: 'distances', 'connectivities'"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["adata"]}, {"cell_type": "markdown", "id": "ad07a3c3-9086-484b-8447-adea76ce2e45", "metadata": {}, "source": ["## Save data\n", "This data, with the parameters chosen in this tutorial, can also be assessed by calling `rgv.datasets.murine_nc(data_type = \"preprocessed\")`"]}, {"cell_type": "code", "execution_count": 13, "id": "534c33f0-197a-424c-928e-9c5215fe2660", "metadata": {}, "outputs": [], "source": ["adata.write_h5ad(\"adata_processed_velo.h5ad\")"]}, {"cell_type": "code", "execution_count": null, "id": "1d0da013-f4d5-4efd-9145-ba497056581b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "regvelo-py310-v2", "language": "python", "name": "regvelo-py310-v2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}