{"cells": [{"cell_type": "markdown", "id": "37f7b867-f3e7-4d1a-ad57-36898e03a0d8", "metadata": {}, "source": ["# Infer prior GRN from [pySCENIC](https://pyscenic.readthedocs.io/en/latest/installation.html)\n", "In this notebook, we use [SCENIC](https://scenic.aertslab.org/) to infer a prior gene regulatory network (GRN) for the RegVelo pipeline."]}, {"cell_type": "markdown", "id": "405f8373-e7d0-4ebe-b03f-3937d1aa9d46", "metadata": {}, "source": ["## Library import"]}, {"cell_type": "code", "execution_count": 1, "id": "b3b40717-9fb5-4ef4-8733-6fb7f4dff658", "metadata": {"tags": []}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import pandas as pd\n", "import scanpy as sc\n", "import loompy as lp\n", "\n", "import glob"]}, {"cell_type": "code", "execution_count": 2, "id": "286984e3-5844-418b-bd2e-70eda9c109a1", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/yifan.chen/miniconda3/envs/pyscenic/lib/python3.10/site-packages/session_info/main.py:213: UserWarning: The '__version__' attribute is deprecated and will be removed in MarkupSafe 3.1. Use feature detection, or `importlib.metadata.version(\"markupsafe\")`, instead.\n", "  mod_version = _find_version(mod.__version__)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-----\n", "anndata     0.11.4\n", "scanpy      1.10.4\n", "-----\n", "PIL                 11.2.1\n", "asttokens           NA\n", "charset_normalizer  3.4.1\n", "cloudpickle         3.1.1\n", "comm                0.2.1\n", "cycler              0.12.1\n", "cython_runtime      NA\n", "cytoolz             1.0.1\n", "dask                2025.4.1\n", "dateutil            2.9.0.post0\n", "debugpy             1.8.11\n", "decorator           5.1.1\n", "exceptiongroup      1.2.0\n", "executing           0.8.3\n", "h5py                3.13.0\n", "ipykernel           6.29.5\n", "jedi                0.19.2\n", "jinja2              3.1.6\n", "joblib              1.4.2\n", "kiwisolver          1.4.8\n", "legacy_api_wrap     NA\n", "llvmlite            0.44.0\n", "loompy              3.0.8\n", "lz4                 4.4.4\n", "markupsafe          3.0.2\n", "matplotlib          3.10.1\n", "mpl_toolkits        NA\n", "natsort             8.4.0\n", "numba               0.61.2\n", "numexpr             2.10.2\n", "numpy               2.2.5\n", "numpy_groupies      0.11.2\n", "packaging           24.2\n", "pandas              2.2.3\n", "parso               0.8.4\n", "platformdirs        4.3.7\n", "prompt_toolkit      3.0.43\n", "psutil              5.9.0\n", "pure_eval           0.2.2\n", "pyarrow             20.0.0\n", "pydev_ipython       NA\n", "pydevconsole        NA\n", "pydevd              3.2.3\n", "pydevd_file_utils   NA\n", "pydevd_plugins      NA\n", "pydevd_tracing      NA\n", "pygments            2.19.1\n", "pyparsing           3.2.3\n", "pytz                2025.2\n", "scipy               1.15.2\n", "session_info        v1.0.1\n", "six                 1.17.0\n", "sklearn             1.6.1\n", "stack_data          0.2.0\n", "tblib               3.1.0\n", "threadpoolctl       3.6.0\n", "tlz                 1.0.1\n", "toolz               1.0.0\n", "tornado             6.4.2\n", "traitlets           5.14.3\n", "typing_extensions   NA\n", "wcwidth             0.2.5\n", "yaml                6.0.2\n", "zipp                NA\n", "zmq                 26.2.0\n", "zoneinfo            NA\n", "-----\n", "IPython             8.30.0\n", "jupyter_client      8.6.3\n", "jupyter_core        5.7.2\n", "-----\n", "Python 3.10.16 (main, Dec 11 2024, 16:24:50) [GCC 11.2.0]\n", "Linux-5.14.0-427.37.1.el9_4.x86_64-x86_64-with-glibc2.34\n", "-----\n", "Session information updated at 2025-04-28 11:30\n"]}], "source": ["sc.settings.verbosity = 3\n", "sc.logging.print_versions()"]}, {"cell_type": "markdown", "id": "943265ea-267c-4c47-b00c-28ef7fbd8ab2", "metadata": {}, "source": ["## Load data and output to loom file\n", "Read murine neural crest data."]}, {"cell_type": "code", "execution_count": null, "id": "6f4a000b-455a-4625-a3e2-c0b2bafc5cea", "metadata": {"tags": []}, "outputs": [], "source": ["adata = rgv.datasets.murine_nc(data_type = \"normalized\")"]}, {"cell_type": "code", "execution_count": 4, "id": "b7ce4c29-e8b3-428d-a930-469a155bd6f0", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AnnData object with n_obs × n_vars = 6788 × 30717\n", "    obs: 'nCount_RNA', 'nFeature_RNA', 'cell_id', 'UMI_count', 'gene_count', 'major_trajectory', 'celltype_update', 'UMAP_1', 'UMAP_2', 'UMAP_3', 'UMAP_2d_1', 'UMAP_2d_2', 'terminal_state', 'nCount_intron', 'nFeature_intron'\n", "    var: 'vf_vst_counts_mean', 'vf_vst_counts_variance', 'vf_vst_counts_variance.expected', 'vf_vst_counts_variance.standardized', 'vf_vst_counts_variable', 'vf_vst_counts_rank', 'var.features', 'var.features.rank'\n", "    obsm: 'X_pca', 'X_umap'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["adata"]}, {"cell_type": "code", "execution_count": 5, "id": "56baecec-0ee1-4f30-bddb-31426b2e3b35", "metadata": {"tags": []}, "outputs": [], "source": ["adata = sc.AnnData(adata.X, obs=adata.obs, var=adata.var)\n", "adata.var[\"<PERSON>\"] = adata.var_names\n", "adata.obs[\"CellID\"] =  adata.obs_names\n", "adata.write_loom(\"adata.loom\")"]}, {"cell_type": "markdown", "id": "2005e02a-abb1-4db9-b944-fe9fdb8ac9f5", "metadata": {}, "source": ["## SCENIC steps\n", "In the following, we use [SCENIC](https://scenic.aertslab.org/) to infer prior regulation information. Installation and usage steps are given in [pySCENIC](https://pyscenic.readthedocs.io/en/latest/installation.html) and are demonstrated in [SCENICprotocol](https://github.com/aertslab/SCENICprotocol/tree/master)."]}, {"cell_type": "code", "execution_count": 7, "id": "64e1477e-6539-4605-abbf-c7f72ddfb8dc", "metadata": {"tags": []}, "outputs": [], "source": ["# path to loom file created previously\n", "f_loom_path_scenic = \"adata.loom\"\n", "# path to list of transcription factors\n", "f_tfs = \"allTFs_mm.txt\""]}, {"cell_type": "code", "execution_count": 9, "id": "6426c2fe-a664-479e-9113-45fbfb62bcef", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/bin/bash: line 1: pyscenic: command not found\n"]}], "source": ["!pyscenic grn {f_loom_path_scenic} {f_tfs} -o \"adj.csv\" --num_workers 24"]}, {"cell_type": "code", "execution_count": null, "id": "1f9fc0ba-0012-48eb-bcbd-3f859dcc37a8", "metadata": {"tags": []}, "outputs": [], "source": ["# path to ranking databases in feather format\n", "f_db_glob = \"scenic/cisTarget_databases/*feather\"\n", "f_db_names = ' '.join( glob.glob(f_db_glob) )\n", "\n", "# path to motif databases\n", "f_motif_path = \"scenic/cisTarget_databases/motifs-v9-nr.mgi-m0.001-o0.0.tbl\""]}, {"cell_type": "code", "execution_count": null, "id": "e1bdaf1e-91e2-49ff-91fd-4143bbdc032a", "metadata": {"tags": []}, "outputs": [], "source": ["!pyscenic ctx \"adj.csv\" \\\n", "    {f_db_names} \\\n", "    --annotations_fname {f_motif_path} \\\n", "    --expression_mtx_fname {f_loom_path_scenic} \\\n", "    --output \"reg.csv\" \\\n", "    --all_modules \\\n", "    --num_workers 24"]}, {"cell_type": "code", "execution_count": null, "id": "6c2f499b-b032-4afd-bb12-57eae82d9a46", "metadata": {"tags": []}, "outputs": [], "source": ["f_pyscenic_output = \"pyscenic_output_all_regulon_no_mask.loom\""]}, {"cell_type": "code", "execution_count": null, "id": "db083910-2345-4e39-8ddd-ff14f210c498", "metadata": {"tags": []}, "outputs": [], "source": ["!pyscenic aucell \\\n", "    {f_loom_path_scenic} \\\n", "    \"reg.csv\" \\\n", "    --output {f_pyscenic_output} \\\n", "    --num_workers 4"]}, {"cell_type": "code", "execution_count": 26, "id": "fbba0c0c-109a-4112-a1eb-340c3942a3b7", "metadata": {"tags": []}, "outputs": [], "source": ["# collect SCENIC AUCell output\n", "lf = lp.connect(f_pyscenic_output, mode='r+', validate=False )\n", "auc_mtx = pd.DataFrame(lf.ca.RegulonsAUC, index=lf.ca.CellID)\n", "regulons = lf.ra.<PERSON>ulons"]}, {"cell_type": "code", "execution_count": 27, "id": "7a0925e3-d1da-4158-a54a-44535613c6c0", "metadata": {"tags": []}, "outputs": [], "source": ["res = pd.concat([pd.Series(r.tolist(), index=regulons.dtype.names) for r in regulons], axis=1)\n", "res.columns = lf.row_attrs[\"SYMBOL\"]\n", "res.to_csv(\"regulon_mat_all_regulons.csv\")"]}, {"cell_type": "markdown", "id": "512bff3d-6cfb-4dd0-a332-703f867943af", "metadata": {}, "source": ["## Create prior <PERSON><PERSON><PERSON> for RegVelo\n", "In the following, we preprocess the GRN inferred from [SCENIC](https://scenic.aertslab.org/), saved as `regulon_mat_all_regulons.csv`. We first read the regulon file, where rows are regulators and columns are target genes. We further extract the names of the transcription factors (TFs) from the row indices using a regex and collapse dublicte TFs by summing their rows."]}, {"cell_type": "code", "execution_count": null, "id": "f5f040df-aff8-43ff-91b4-b4e7ff6dbf18", "metadata": {}, "outputs": [], "source": ["# load saved regulon-target matrix\n", "reg = pd.read_csv(\"regulon_mat_all_regulons.csv\", index_col = 0)\n", "\n", "reg.index = reg.index.str.extract(r\"(\\w+)\")[0]\n", "reg = reg.groupby(reg.index).sum()"]}, {"cell_type": "markdown", "id": "5c18d266-5726-4ecc-ab38-2de5ce0d185b", "metadata": {}, "source": ["We further binarize the matrix, where 1 indicates the presence of regulation and 0 indicates otherwise and get the list of TFs and genes."]}, {"cell_type": "code", "execution_count": null, "id": "bd475a15-b5b7-49a9-b549-c7fece33ba81", "metadata": {}, "outputs": [], "source": ["reg[reg != 0] = 1\n", "\n", "TF = np.unique(list(map(lambda x: x.split(\"(\")[0], reg.index.tolist())))\n", "genes = np.unique(TF.tolist() + reg.columns.tolist())"]}, {"cell_type": "markdown", "id": "4a9633d5-204a-431f-a121-7cd927604b72", "metadata": {}, "source": ["For the prior GRN, we first construct an empty square matrix and populate it based on the previously binarized regulation information. We further remove the genes that are neither a TF nor a target gene (i.e. remove empty rows and comlumns) and save the cleaned and structured GRN to a `.parquet` file for RegVelo's downstream pipeline."]}, {"cell_type": "code", "execution_count": 76, "id": "dd55d3a2-f98e-42a3-849a-22471675db2d", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Done! processed GRN with 543 TF and 30717 targets\n"]}], "source": ["GRN = pd.DataFrame(0, index=genes, columns=genes)\n", "GRN.loc[TF,reg.columns.tolist()] = np.array(reg)\n", "\n", "mask = (GRN.sum(0) != 0) | (GRN.sum(1) != 0)\n", "GRN = GRN.loc[mask, mask].copy()\n", "\n", "GRN.to_parquet(\"regulon_mat_processed_all_regulons.parquet\")\n", "print(\"Done! processed GRN with \" + str(reg.shape[0]) + \" TFs and \" + str(reg.shape[1]) + \" targets\")"]}, {"cell_type": "code", "execution_count": null, "id": "5a5ff903-a13d-400c-9dcc-d2d75bd4d4f7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python (pyscenic)", "language": "python", "name": "pyscenic"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}