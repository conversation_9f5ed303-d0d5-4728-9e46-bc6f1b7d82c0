{"cells": [{"cell_type": "markdown", "id": "bb21ffe1", "metadata": {}, "source": ["# Model selection: select proper training strategy and regularization parameter to fit given data."]}, {"cell_type": "markdown", "id": "56d76bdb", "metadata": {}, "source": ["In training session, regvelo provide different training strategy for users, in order to meet different modeling preference or data characters. For `hard` mode, the GRN structure is strictly determined by prior knowledge, which means there no new interaction between genes and TFs can be build. However, for `soft` mode, these interaction are allowed to be build, according to data. The preference between prior knowledge and data character, are weighted by regularization parameter, lambda2. These function are packaged as a class named `ModelComparison`, includes three main functions, `.train`, `.evaluate` and `plot_results`."]}, {"cell_type": "markdown", "id": "4485de33", "metadata": {}, "source": ["### Library import"]}, {"cell_type": "code", "execution_count": 2, "id": "b44fa88b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Seed set to 0\n"]}, {"data": {"text/plain": ["AnnData object with n_obs × n_vars = 697 × 988\n", "    obs: 'initial_size_unspliced', 'initial_size_spliced', 'initial_size', 'n_counts', 'cell_type', 'stage', 'cell_type2', 'macrostates', 'latent_time', 'stage_num', 'macrostates_fwd', 'term_states_fwd', 'term_states_fwd_probs'\n", "    var: 'Accession', 'Chromosome', 'End', 'Start', 'Strand', 'gene_count_corr', 'is_tf', 'TF', 'velocity_genes', 'fit_beta', 'fit_gamma', 'fit_scaling'\n", "    uns: '_scvi_manager_uuid', '_scvi_uuid', 'cell_type_colors', 'coarse_fwd', 'eigendecomposition_fwd', 'macrostates_colors', 'macrostates_fwd_colors', 'neighbors', 'network', 'regulators', 'schur_matrix_fwd', 'skeleton', 'targets', 'term_states_fwd_colors'\n", "    obsm: 'X_pca', 'X_umap', 'macrostates_fwd_memberships', 'schur_vectors_fwd', 'term_states_fwd_memberships'\n", "    layers: 'Ms', 'Mu', 'ambiguous', 'fit_t', 'latent_time_velovi', 'matrix', 'spliced', 'unspliced', 'velocity'\n", "    obsp: 'connectivities', 'distances'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# Basic packages\n", "import numpy as np\n", "import pandas as pd\n", "import scipy\n", "import torch\n", "import matplotlib.pyplot as plt\n", "import mplscience\n", "import seaborn as sns\n", "from scipy.stats import ttest_rel\n", "\n", "# RegVelo and related package\n", "import scanpy as sc\n", "import cellrank as cr\n", "import scvi\n", "from regvelo import REGVELOVI\n", "import scvelo as scv\n", "from regvelo import ModelComparison # Import ModelComparison\n", "\n", "# Initialize random seed\n", "scvi.settings.seed = 0\n", "\n", "# Data loading\n", "adata = sc.read_h5ad(\"/home/<USER>/RegVelo/regvelo_reproducibility/data/zebrafish/processed/adata_run_regvelo.h5ad\")\n", "adata"]}, {"cell_type": "markdown", "id": "e911e1cc", "metadata": {}, "source": ["The `adata` loaded in this step are after trained. In fact, only preprocessed adata is enough for run this step, because new training session will be done in following steps. Recommand preprocessing steps can be found [here](https://regvelo.readthedocs.io/en/latest/tutorials/murine/02_<PERSON><PERSON><PERSON>_preparation.html). \n", "\n", "However, in order to evaluate in different perspectives, we recommand you to run following side information compatation step."]}, {"cell_type": "markdown", "id": "db56e3cb", "metadata": {}, "source": ["### Side information before creating ModelComparison object.\n", "There are several types of information that you may need in following steps. We recommand you to supply all information needed before creating ModelComparison object.\n", "\n", "### Pseudotime and Stemness Score.\n", "Pseudotime indicates a simulated timepoint to each cell, ranking them in temporal order. \n", "Stemness score indicates the differentiation potency of cells, measuring how far a cell is compared to stem cell.\n", "These steps provide pseudotime (via CellRank DPT kernel, see [here](https://cellrank.readthedocs.io/en/stable/notebooks/tutorials/kernels/300_pseudotime.html)) and stemness score (via CellRank CytoTrace kernel, see [here](https://cellrank.readthedocs.io/en/stable/notebooks/tutorials/kernels/400_cytotrace.html)).\n", "\n", "In fact, you can also use other methods to compute pseudo time or stemness score for each cell, and store those information in `adata.obs`. `ModelComparison` will also get your information in following `.evaluate` step.  \n", "\n", "These two are not necessary, if you don't use pseudotime or stemness to evaluate model in following steps. \n", "\n", " "]}, {"cell_type": "code", "execution_count": 3, "id": "ff6a8dec", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "20fbcd18243d4740bfe30ddca4724050", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5331201b1af24215a02342824465dfe9", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "15ec7d6c816145ab9a438ce78c046cf8", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["CytoTRACEKernel[n=697, dnorm=False, scheme='soft', b=10.0, nu=0.5]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Diffusion pseudotime computation\n", "## In this step we just consider a random cell whose stage_num is 3 as root cell. You can also use other method to refer your root cell, if you also use DPT method provided by scanpy.\n", "root_cell_name = adata.obs[adata.obs['stage_num'] == 3.0].index[0] # Get cell name\n", "root_ix = np.where(adata.obs_names == root_cell_name)[0][0] # Get cell index\n", "adata.uns[\"iroot\"] = root_ix\n", "sc.tl.diffmap(adata)\n", "sc.tl.dpt(adata)\n", "\n", "# Stemness Score computation\n", "vk = cr.kernels.VelocityKernel(adata)\n", "vk.compute_transition_matrix()\n", "ck = cr.kernels.ConnectivityKernel(adata).compute_transition_matrix()\n", "kernel = 0.8 * vk + 0.2 * ck\n", "ctk = cr.kernels.CytoTRACEKernel(adata).compute_cytotrace()\n", "ctk.compute_transition_matrix(threshold_scheme=\"soft\", nu=0.5)"]}, {"cell_type": "markdown", "id": "5096ec07", "metadata": {}, "source": ["### Terminal states and transition.\n", "\n", "`terminal states` is a list recording all potential terminal cell type, given manually by user. This will be used, if you use `TSI` mode in evaluation, otherwise it is not necessary.\n", "\n", "`n_states` refers to the total number of macrostates. Detailed introduction can be found [here](https://cellrank.readthedocs.io/en/stable/api/_autosummary/estimators/cellrank.estimators.GPCCA.html#macrostates) or in following content. This wil be used, if you use `TSI` mode in evaluation, otherwise it is not necessary.\n", "\n", "`state_transistion` is a list of transition pair, and each element in transition pair must in type of string. Each pair records the orginal state and terminal state of a state transition event. This will be used, if you use `CBC` mode in evaluation, otherwise it is not necessary. "]}, {"cell_type": "code", "execution_count": 4, "id": "b949c3a8", "metadata": {}, "outputs": [], "source": ["TERMINAL_STATES = [\n", "    \"mNC_head_mesenchymal\",\n", "    \"mNC_arch2\",\n", "    \"mNC_hox34\",\n", "    \"Pigment\",\n", "]\n", "\n", "n_STATES = 8\n", "\n", "STATE_TRANSITION = (('3.0','6.5'),\n", "                    ('6.5','10.0'),\n", "                    ('10.0', '12.5'),\n", "                    ('12.5','17.5'),\n", "                    ('17.5','21.5'))"]}, {"cell_type": "markdown", "id": "a5b783d4", "metadata": {}, "source": ["### ModelComparison: object build."]}, {"cell_type": "code", "execution_count": 5, "id": "70a0ddd3", "metadata": {}, "outputs": [], "source": ["comp = ModelComparison(terminal_states=TERMINAL_STATES, state_transition=STATE_TRANSITION, n_states=n_STATES)"]}, {"cell_type": "markdown", "id": "499410dd", "metadata": {}, "source": ["### ModelComparison: train.\n", "\n", "In this step you will begin to train models that you are interested in. All training strategies are stored in `model_list`. As introduced previously, `soft` mode enable to create new connection within GRN, `hard` mode don't. For `soft_regularized` mode, you can put a series of lambda value in `lam2` list accompany with soft_regularized models. A larger lambda2 value inficates a bigger preference on current data, and a smaller preference on prior knowledge. You can let model to be trained multiple times under the same circumstance, by ajusting `n_repeats`, to have multiple output results. Be aware that large number of `n_repeats` will cause huge amount of training time. "]}, {"cell_type": "code", "execution_count": 6, "id": "51d4e662", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Seed set to 0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "HPU available: False, using: 0 HPUs\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "You are using a CUDA device ('NVIDIA A30') that has Tensor Cores. To properly utilize them, you should set `torch.set_float32_matmul_precision('medium' | 'high')` which will trade-off precision for performance. For more details, read https://pytorch.org/docs/stable/generated/torch.set_float32_matmul_precision.html#torch.set_float32_matmul_precision\n", "LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'train_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/loops/fit_loop.py:310: The number of training batches (1) is smaller than the logging interval Trainer(log_every_n_steps=10). Set a lower value for log_every_n_steps if you want to see logs for the training epoch.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'val_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8549c7bc013047a4a5bb6e32830251e0", "version_major": 2, "version_minor": 0}, "text/plain": ["Training:   0%|          | 0/1500 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Seed set to 1\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "HPU available: False, using: 0 HPUs\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Monitored metric elbo_validation did not improve in the last 45 records. Best score: -2518.839. Signaling Trainer to stop.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'train_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/loops/fit_loop.py:310: The number of training batches (1) is smaller than the logging interval Trainer(log_every_n_steps=10). Set a lower value for log_every_n_steps if you want to see logs for the training epoch.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'val_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dc0abef60008418c967de69a265b2825", "version_major": 2, "version_minor": 0}, "text/plain": ["Training:   0%|          | 0/1500 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Seed set to 2\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "HPU available: False, using: 0 HPUs\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Monitored metric elbo_validation did not improve in the last 45 records. Best score: -2454.206. Signaling Trainer to stop.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'train_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/loops/fit_loop.py:310: The number of training batches (1) is smaller than the logging interval Trainer(log_every_n_steps=10). Set a lower value for log_every_n_steps if you want to see logs for the training epoch.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'val_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9a3fbbf55143427fb56712e08a9fe2b9", "version_major": 2, "version_minor": 0}, "text/plain": ["Training:   0%|          | 0/1500 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Seed set to 0\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "HPU available: False, using: 0 HPUs\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Monitored metric elbo_validation did not improve in the last 45 records. Best score: -2489.106. Signaling Trainer to stop.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'train_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/loops/fit_loop.py:310: The number of training batches (1) is smaller than the logging interval Trainer(log_every_n_steps=10). Set a lower value for log_every_n_steps if you want to see logs for the training epoch.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'val_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ef55f881fcdd4cefb66f0dfdd9328b5c", "version_major": 2, "version_minor": 0}, "text/plain": ["Training:   0%|          | 0/1500 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Seed set to 1\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "HPU available: False, using: 0 HPUs\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Monitored metric elbo_validation did not improve in the last 45 records. Best score: -2371.138. Signaling Trainer to stop.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'train_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/loops/fit_loop.py:310: The number of training batches (1) is smaller than the logging interval Trainer(log_every_n_steps=10). Set a lower value for log_every_n_steps if you want to see logs for the training epoch.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'val_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0cbabae2061d495c9bb5fc1903f99e00", "version_major": 2, "version_minor": 0}, "text/plain": ["Training:   0%|          | 0/1500 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Seed set to 2\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "HPU available: False, using: 0 HPUs\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Monitored metric elbo_validation did not improve in the last 45 records. Best score: -2260.451. Signaling Trainer to stop.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'train_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/loops/fit_loop.py:310: The number of training batches (1) is smaller than the logging interval Trainer(log_every_n_steps=10). Set a lower value for log_every_n_steps if you want to see logs for the training epoch.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'val_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dfe11cda970046c497838d5d8be86e28", "version_major": 2, "version_minor": 0}, "text/plain": ["Training:   0%|          | 0/1500 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Seed set to 0\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "HPU available: False, using: 0 HPUs\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Monitored metric elbo_validation did not improve in the last 45 records. Best score: -2435.288. Signaling Trainer to stop.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'train_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/loops/fit_loop.py:310: The number of training batches (1) is smaller than the logging interval Trainer(log_every_n_steps=10). Set a lower value for log_every_n_steps if you want to see logs for the training epoch.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'val_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "97a26d7797d348059dce205456a7c74a", "version_major": 2, "version_minor": 0}, "text/plain": ["Training:   0%|          | 0/1500 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "HPU available: False, using: 0 HPUs\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Monitored metric elbo_validation did not improve in the last 45 records. Best score: -2527.615. Signaling Trainer to stop.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'train_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/loops/fit_loop.py:310: The number of training batches (1) is smaller than the logging interval Trainer(log_every_n_steps=10). Set a lower value for log_every_n_steps if you want to see logs for the training epoch.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'val_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "54703306eada4f86b6237670493ca991", "version_major": 2, "version_minor": 0}, "text/plain": ["Training:   0%|          | 0/1500 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "HPU available: False, using: 0 HPUs\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Monitored metric elbo_validation did not improve in the last 45 records. Best score: -2431.752. Signaling Trainer to stop.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'train_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/loops/fit_loop.py:310: The number of training batches (1) is smaller than the logging interval Trainer(log_every_n_steps=10). Set a lower value for log_every_n_steps if you want to see logs for the training epoch.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'val_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6fdbfc35493242ce80222d099f87b81a", "version_major": 2, "version_minor": 0}, "text/plain": ["Training:   0%|          | 0/1500 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Seed set to 1\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "HPU available: False, using: 0 HPUs\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Monitored metric elbo_validation did not improve in the last 45 records. Best score: -2132.919. Signaling Trainer to stop.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'train_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/loops/fit_loop.py:310: The number of training batches (1) is smaller than the logging interval Trainer(log_every_n_steps=10). Set a lower value for log_every_n_steps if you want to see logs for the training epoch.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'val_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "996622ce512547fca7d23ae8738b20cd", "version_major": 2, "version_minor": 0}, "text/plain": ["Training:   0%|          | 0/1500 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "HPU available: False, using: 0 HPUs\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Monitored metric elbo_validation did not improve in the last 45 records. Best score: -2388.154. Signaling Trainer to stop.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'train_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/loops/fit_loop.py:310: The number of training batches (1) is smaller than the logging interval Trainer(log_every_n_steps=10). Set a lower value for log_every_n_steps if you want to see logs for the training epoch.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'val_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6a90bf43b5c04e06aef73ba6ee7a7054", "version_major": 2, "version_minor": 0}, "text/plain": ["Training:   0%|          | 0/1500 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "HPU available: False, using: 0 HPUs\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Monitored metric elbo_validation did not improve in the last 45 records. Best score: -2263.319. Signaling Trainer to stop.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'train_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/loops/fit_loop.py:310: The number of training batches (1) is smaller than the logging interval Trainer(log_every_n_steps=10). Set a lower value for log_every_n_steps if you want to see logs for the training epoch.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'val_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "083578d698e94f8ab2a2218448819cee", "version_major": 2, "version_minor": 0}, "text/plain": ["Training:   0%|          | 0/1500 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Seed set to 2\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "HPU available: False, using: 0 HPUs\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Monitored metric elbo_validation did not improve in the last 45 records. Best score: -2082.085. Signaling Trainer to stop.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'train_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/loops/fit_loop.py:310: The number of training batches (1) is smaller than the logging interval Trainer(log_every_n_steps=10). Set a lower value for log_every_n_steps if you want to see logs for the training epoch.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'val_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4308d94716234c62ab7defb98c85fb22", "version_major": 2, "version_minor": 0}, "text/plain": ["Training:   0%|          | 0/1500 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "HPU available: False, using: 0 HPUs\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Monitored metric elbo_validation did not improve in the last 45 records. Best score: -2471.712. Signaling Trainer to stop.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'train_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/loops/fit_loop.py:310: The number of training batches (1) is smaller than the logging interval Trainer(log_every_n_steps=10). Set a lower value for log_every_n_steps if you want to see logs for the training epoch.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'val_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ebbdae22646d47689353a629a9d0749f", "version_major": 2, "version_minor": 0}, "text/plain": ["Training:   0%|          | 0/1500 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "HPU available: False, using: 0 HPUs\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run Lightning on SLURM, prepend your python command with `srun` like so: srun python /home/<USER>/miniforge3/envs/regvelo-py310/lib/python ...\n", "LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Monitored metric elbo_validation did not improve in the last 45 records. Best score: -2485.853. Signaling Trainer to stop.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'train_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/loops/fit_loop.py:310: The number of training batches (1) is smaller than the logging interval Trainer(log_every_n_steps=10). Set a lower value for log_every_n_steps if you want to see logs for the training epoch.\n", "/home/<USER>/miniforge3/envs/regvelo-py310/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/data_connector.py:425: The 'val_dataloader' does not have many workers which may be a bottleneck. Consider increasing the value of the `num_workers` argument` to `num_workers=127` in the `DataLoader` to improve performance.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ba10879a03884418b8e160f581b9c6a6", "version_major": 2, "version_minor": 0}, "text/plain": ["Training:   0%|          | 0/1500 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Monitored metric elbo_validation did not improve in the last 45 records. Best score: -2333.958. Signaling Trainer to stop.\n"]}, {"data": {"text/plain": ["['soft_0',\n", " 'soft_1',\n", " 'soft_2',\n", " 'hard_0',\n", " 'hard_1',\n", " 'hard_2',\n", " 'soft_regularized\\nlam2:0.3_0',\n", " 'soft_regularized\\nlam2:0.5_0',\n", " 'soft_regularized\\nlam2:0.8_0',\n", " 'soft_regularized\\nlam2:0.3_1',\n", " 'soft_regularized\\nlam2:0.5_1',\n", " 'soft_regularized\\nlam2:0.8_1',\n", " 'soft_regularized\\nlam2:0.3_2',\n", " 'soft_regularized\\nlam2:0.5_2',\n", " 'soft_regularized\\nlam2:0.8_2']"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["comp.train(adata,\n", "           model_list=['soft','hard','soft_regularized'],\n", "           lam2=[0.3,0.5,0.8],\n", "           n_repeat=3)"]}, {"cell_type": "markdown", "id": "a74b03f3", "metadata": {}, "source": ["### ModelComparison: evaluate and plotting.\n", "\n", "After training, all models has been reserved in the object, so you don't need to train them again and over again when evaluating. Here, we provide five perspectives to evaluate how good those models are. After evaluating, you can plot results by barplot using `.plot_results`, with the best performed model marked and significance bar marked compared between the best one and the others. Significance bar will only be marked if `n_repeat` >= 3, and the p-value < 0.05 in comparison."]}, {"cell_type": "markdown", "id": "59f6fd14", "metadata": {}, "source": ["### Real time\n", "\n", "If you know the real cell type of each cell, and the temporal sequence of each cell type, you can directly use `Real_Time` mode. Under this mode, `side_key` must be provided, stored in `adata.obs`, which must be a series of float number for calculating spearmann correlation with RegVelo latent time."]}, {"cell_type": "code", "execution_count": 7, "id": "f107c9d9", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 600x450 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["comp.evaluate(side_information='Real_Time',\n", "              side_key='stage_num')\n", "comp.plot_results(side_information='Real_Time')"]}, {"cell_type": "markdown", "id": "6c73a0b3", "metadata": {}, "source": ["### Pseudo_Time\n", "\n", "If you have computed pseudo time as above, you can use `Pseudo_Time` mode. Under this mode, `side_key` will be setted as `dpt_pseudotime` as default. You can provide any other side key stored in `adata.obs`, which must be a series of float number for calculating spearmmann correlation with RegVelo latent time."]}, {"cell_type": "code", "execution_count": 8, "id": "458fbf76", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 600x450 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["comp.evaluate(side_information='Pseudo_Time')\n", "comp.plot_results(side_information='Pseudo_Time')"]}, {"cell_type": "markdown", "id": "fce49869", "metadata": {}, "source": ["### Stemness_Score\n", "\n", "If you have computed stemmness score as above, you can use `Stemness_Score` mode. Under this mode, `side_key` will be setted as `ct_score` as default. You can provide any other side key stored in adata.obs, which must be a series of float number for calculating spearmann correlation with RegVelo latent time. "]}, {"cell_type": "code", "execution_count": 9, "id": "f513fd05", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAkAAAAGqCAYAAADjrftPAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjEsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvc2/+5QAAAAlwSFlzAAAPYQAAD2EBqD+naQAAdYBJREFUeJzt3Xtczvf/P/DH1eHqpBTlVJpDBympCOXQpBkZqUZGh835fNjYxMcctvEZ2fZBRhhRyyHN2D7LHEYih2H0UShDijlUJMXV1fX+/eF3XV+XK51Ul7oe99vNbev1fr3f7+f7nVyPXu/X+/0WCYIggIiIiEiDaKm7ACIiIqK6xgBEREREGocBiIiIiDQOAxARERFpHAYgIiIi0jgMQERERKRxGICIiIhI4zAAERERkcZhACIiIiKNwwBE1MB5e3tj7ty5iq9PnToFe3t7nDp1qsb2YW9vj9WrV9fY9ipr7ty5cHV1rfP9vi51nS8i+j866i6AqCFLSEhAeHi44muxWIxWrVqhZ8+emDx5MszNzdVYXdUcPXoUFy9exLRp09RdCtWw7OxsREZG4syZM7h79y5MTEzQpk0bdO/eHdOnT1f0i42NhYGBAQICAtRYbf2Vl5eHtWvXIjk5Gbdv34aRkREsLS3RvXt3TJ48GUZGRuouUaMwABHVgenTp8PKygoSiQRnz55FXFwcjh49il9++QUGBgZ1Wou7uzsuXrwIXV3dKq139OhRxMbGlhmALl68CG1t7ZoqkerQzZs38f7770NPTw+BgYGwsrLCvXv3kJaWhg0bNigFoLi4OJiZmTEAVcPDhw8RGBiIwsJCBAYGol27dnj48CGuXLmCuLg4fPDBBwxAdYwBiKgO9OnTB506dQIADBs2DKampti8eTMOHTqE9957r8x1ioqKYGhoWOO1aGlpQU9Pr0a3WdPbo7qzZcsWFBUVYc+ePbC0tFRalpubq6aqGp74+Hjcvn0bcXFxcHNzU1pWWFhY5V9IXkdt/dtS33AOEJEa9OjRA8DzSw/A/81lycrKwrhx4+Dq6orZs2cDAGQyGbZs2YJBgwahU6dO8PT0xOeff45Hjx4pbVMQBKxduxZ9+vRB586dERISgoyMDJV9v2oO0IULFzBu3Di4u7vDxcUFgwcPRnR0tKK+2NhYAM/nr8j/yJU1pyUtLQ1jx46Fm5sbXF1dERYWhr/++kupT0JCAuzt7XH27FksW7YMPXr0gIuLC6ZMmYK8vLxKn89bt25hzJgxcHFxQa9evbBmzRoIgqDUZ9OmTRgxYgS6d+8OZ2dnBAQEIDExUWVbx48fxwcffICuXbvC1dUV7777Lr755hulPhKJBKtWrcI777wDJycneHl5Yfny5ZBIJCr9li5dih49esDV1RUTJ07EP//8U+njys3Nxbx58+Dp6YlOnTphyJAh+Omnn5T6ZGdnw97eHps2bcKOHTvg4+MDJycnBAYG4uLFixXuIysrC82bN1cJPwDQtGlTxf97e3sjIyMDp0+fVnz/Q0JCFMsLCgrw1VdfwcvLC05OTnjnnXcQFRUFmUxWZq2xsbHo168fOnfujNGjR+POnTsQBAGRkZHo06cPnJ2dMWnSJDx8+FCpJm9vb0yYMAF//vkn3n//fXTq1An9+vXDnj17VOqvTE0A8OuvvyIgIACurq5wc3NT+rsPACUlJVizZg369++PTp06oXv37vjggw9w/PjxCs/vi+dZW1sbLi4uKssaNWqk8ktEeT+PcikpKRg5ciRcXFzQtWtXTJo0CdeuXVPqs3r1atjb2yMzMxOffPIJ3N3dMXLkSMXyn3/+GQEBAXB2dka3bt0wa9Ys3Llzp9LHVZ9xBIhIDbKysgAApqamijapVIoxY8agS5cu+Oyzz6Cvrw8A+Pzzz/HTTz8hICAAISEhyM7ORmxsLNLS0hAXF6f4zfE///kPvv/+e3h5ecHLywuXLl3C6NGjUVJSUmE9x48fx4QJE9CsWTOEhobC3Nwc165dw5EjRxAWFoagoCDcu3cPx48fx/LlyyvcXkZGBkaNGgUjIyOMHTsWOjo62LFjB0JCQhATE4POnTsr9f/yyy9hYmKCqVOnIicnB9HR0ViyZAm+++67CvdVWlqKsWPHonPnzpgzZw6OHTuG1atXo7S0FDNmzFD027p1K7y9vTF48GCUlJTg119/xYwZM7B+/Xq8/fbbironTJgAe3t7TJ8+HWKxGDdv3sS5c+cU25HJZJg0aRLOnj2L4cOHo3379rh69Sqio6Nx48YNrF27VtF3/vz52Lt3L9577z24ubnh5MmTGD9+fIXHBABPnz5FSEgIsrKyMGrUKFhZWSExMRFz585FQUEBwsLClPr/8ssvePLkCYKCgiASibBx40ZMmzYNBw8eLHd0wdLSEikpKUhJSYGHh8cr+82bNw9ffPEFDA0NMXHiRABQzGErLi5GcHAw7t69ixEjRqBly5Y4f/48vvnmG9y/fx/z589X2ta+fftQUlKCkJAQPHz4EBs3bsTMmTPRo0cPnDp1CuPGjcPNmzcRExODr7/+GsuWLVNa/+bNm5gxYwbef/99+Pv7Y/fu3Zg7dy4cHR1ha2tbpZqOHz+Ojz/+GB4eHopfOv7++2+cO3dOcY7XrFmD9evXY9iwYXB2dkZhYSH+97//4dKlS+jZs2eF30v5eS4tLcXPP/8Mf3//cvtW9PMIACdOnMC4ceNgZWWFqVOn4unTp4iJicEHH3yAhIQEWFlZKW1zxowZeOuttzBr1izFLwfff/89/vOf/2DgwIF4//33kZeXh5iYGIwaNQp79uyBiYlJpY6t3hKIqNbs3r1bsLOzE06cOCHk5uYKd+7cEX799VehW7dugrOzs/DPP/8IgiAIn332mWBnZydEREQorX/mzBnBzs5O2Lt3r1J7UlKSUntubq7g6OgojB8/XpDJZIp+33zzjWBnZyd89tlniraTJ08KdnZ2wsmTJwVBEASpVCp4e3sLffv2FR49eqS0nxe3tXjxYsHOzq7M47SzsxNWrVql+Hry5MmCo6OjkJWVpWi7e/eu4OrqKowaNUrl/Hz44YdK+1q6dKng4OAgFBQUlLk/Ofl5++KLL5RqHj9+vODo6Cjk5uYq2ouLi5XWlUgkwnvvvSeEhoYq2jZv3izY2dkprfeyPXv2CB06dBDOnDmj1B4XFyfY2dkJZ8+eFQRBENLT0wU7Ozth0aJFSv0+/vhjlfNVli1btgh2dnbCzz//rFRzUFCQ4OLiIjx+/FgQBEG4deuWYGdnJ3Tr1k14+PChou/BgwcFOzs74fDhw+Xu5+rVq4Kzs7NgZ2cn+Pn5CV9++aVw4MABoaioSKXvoEGDhODgYJX2yMhIwcXFRbh+/bpSe0REhODg4CDcvn1bqdYePXoofW9Xrlwp2NnZCUOGDBFKSkoU7R9//LHg6OgoPHv2TNHWt29fwc7OTun85+bmCk5OTsK///3vKtf05ZdfCm5uboJUKn3lORoyZIgwfvz4Vy6vjPv37ws9evQQ7OzshAEDBgiff/65sG/fPpW/45X9efTz8xM8PDyE/Px8RVt6errQoUMH4dNPP1W0rVq1SrCzsxM+/vhjpW1lZ2cLDg4Owvfff6/UfuXKFaFjx44q7Q0RL4ER1YEPP/wQHh4e8PLywqxZs2BkZIQ1a9agefPmSv0++OADpa8TExNhbGyMnj17Ii8vT/HH0dERhoaGistYJ06cQElJCYKDgyESiRTrvzxKUJa0tDRkZ2cjNDRU5Te+F7dVWaWlpTh+/Dh8fHzQunVrRXuzZs3w3nvv4ezZsygsLFRaZ/jw4Ur76tq1K0pLS5GTk1OpfY4aNUqp5lGjRqGkpAQpKSmKdvmIGgA8evQIjx8/RpcuXZCWlqZolx//oUOHVC6TyCUmJqJ9+/Zo166d0vdEfllT/j05evQoAChdJgIq9z0BgKSkJFhYWCjNEdPV1UVISAiKiopw5swZpf6+vr5o3Lix4uuuXbsCeH55sDy2trbYs2cPhgwZgpycHGzduhVTpkyBp6cndu7cWalaExMT0aVLF5iYmCidE09PT5SWlqrUOmDAABgbGyu+dnZ2BgAMGTIEOjo6Su0lJSW4e/eu0vo2NjaK4wOAJk2aoG3btkrHWtmaTExMUFxcXO7lLBMTE2RkZODGjRuVOh9lMTc3x88//4wRI0agoKAA27dvxyeffAIPDw9ERkYqRmUq8/N47949pKenw9/fX2kUuUOHDvD09FT83XvRiBEjlL4+cOAAZDIZBg4cqHR+zM3N8dZbb9XoYzLeVLwERlQHPv/8c7Rt2xba2towNzdH27ZtoaWl/PuHjo4OWrRoodR28+ZNPH78+JWXJuSTVG/fvg0AaNOmjdLyJk2aKH0olkX+oWFnZ1fp4ylPXl4eiouL0bZtW5Vl7du3h0wmw507dxSXKgCgVatWSv3k//AXFBRUuD8tLS2loAVAse8XA9Qff/yB77//Hunp6UpzdV4MXr6+vti1axf+9a9/YeXKlfDw8MA777yDAQMGKL5fN2/exLVr1yr8nuTk5EBLSwvW1tZKy9u1a1fhMcnXf+utt1T+nrRv3x7A/33P5Vq2bKn0tfz7Xplz2LZtW6xYsQKlpaXIzMzEkSNHsHHjRixYsABWVlbw9PQsd/2bN2/iypUrrzwnL8/nerlWeRh6VfujR4+Uvscv9wOeH++L8+IqW9PIkSPx22+/Ydy4cWjevDl69uyJgQMHok+fPoq+06dPx+TJk/Huu+/Czs4OvXr1gp+fHzp06FDmtl+lWbNmWLx4MRYtWoQbN24gOTkZGzZswKpVq9CsWTMMGzasUj+P8u/9q37GkpOTVSY6v3xJ7MaNGxAEAf379y9zHy8G0Yaq4R8h0RvA2dlZcRfYq4jFYpUPO5lMhqZNmyIiIqLMdZo0aVJjNarTy8ctJ7w0kbm6/vzzT0yaNAnu7u5YuHAhLCwsoKuri927d+OXX35R9NPX10dsbCxOnTqFI0eO4NixY/jvf/+LHTt24IcffoC2tjZkMhns7OyUnu/0opdDbF151WMIqnIOtbW1FROcXVxcEBoain379lUYgGQyGXr27ImxY8eWufzlYP6qWiv796Ayj1yobE1NmzbFnj17kJycjKSkJCQlJSEhIQFDhw7F119/DeD5oyMOHDiAQ4cO4fjx44iPj0d0dDQWL16MYcOGVVjLy0QiEdq2bYu2bdvi7bffRv/+/bF3795qbauyXp5kLZPJIBKJsGHDhjLPpybcJcYARPQGs7a2RkpKCtzc3JQu4bxMPoJy48YNpd+U8/LyVO4We5m8/9WrV8v9oKvs5bAmTZrAwMAA169fV1n2999/Q0tLq8zf4KtLJpPh1q1bSr8Ny/ctv7Np//790NPTw6ZNmyAWixX9du/erbI9LS0teHh4wMPDA+Hh4Vi3bh2+/fZbnDp1Cp6enrC2tsbly5fh4eFR7jmxtLSETCZDVlaW0qjP33//XanjsrS0xJUrVyCTyZSCgXz9l0fNapqTkxOA55db5F51vNbW1igqKqowKNWlqtQkFovh7e0Nb29vyGQyLFq0CDt27MDkyZPx1ltvAXh+w0JgYCACAwPx5MkTBAcHY/Xq1a8dWlq3bg0TExPcv39f8TVQ/s+j/Hv/qp8xMzOzCgOMtbU1BEGAlZVVmSNJmoBzgIjeYAMHDkRpaanSnUVyUqlUcXnD09MTurq6iImJUfpt+eXbZsvi6OgIKysrbN26VeVyyYvbkj+wsaJLKtra2ujZsycOHTqkuM0fAB48eIBffvkFXbp0QaNGjSqsqyrkt+jLa46NjYWurq7i8oe2tjZEIhFKS0sV/bKzs3Ho0CGl7bx8yzUAODg4AIDistnAgQNx9+7dMufHPH36FEVFRQCguISybds2pT6V+Z7I179//z7++9//KtqkUim2bdsGQ0NDuLu7V2o7Ffnzzz/LvFNQPo/kxQ9HAwODMr//AwcOxPnz53Hs2DGVZQUFBZBKpTVSa1VUtqb8/HylZVpaWopHPMi/5y/3MTIygrW1tcpjD8pz4cIFxd+NF128eBEPHz5UnOfK/Dw2a9YMDg4O2LNnj1Kfq1ev4vjx4/Dy8qqwnv79+0NbW7vMR0YIgqByzA0RR4CI3mDdunVDUFAQ1q9fj/T0dPTs2RO6urq4ceMGEhMTMX/+fAwYMABNmjTB6NGjsX79ekyYMAFeXl5IS0tDUlISzMzMyt2HlpYWFi1ahEmTJmHo0KEICAiAhYUF/v77b2RmZmLTpk0Anv/DDDy/Zb1Xr17Q1tbGoEGDytzmzJkzceLECYwcORIjR46EtrY2duzYAYlEgjlz5tToOdLT08OxY8fw2WefwdnZGceOHcORI0cwceJExSVCLy8vbN68GWPHjsV7772H3Nxc/Pjjj7C2tsaVK1cU24qMjMSff/4JLy8vWFpaKvq1aNECXbp0AQD4+fnht99+w8KFC3Hq1Cm4ubmhtLQUf//9NxITE7Fx40Z06tQJDg4OeO+99/Djjz/i8ePHcHV1xcmTJ3Hz5s1KHVdQUBB27NiBuXPn4tKlS7C0tMT+/ftx7tw5zJs3r8ZC5IYNG3Dp0iW88847ig/+tLQ07NmzB6ampkqTth0dHREXF4e1a9firbfeQpMmTeDh4YExY8bg8OHDmDhxIvz9/eHo6Iji4mJcvXoV+/fvx6FDh+r8cm1la/rXv/6FR48eoUePHmjevDlu376NmJgYODg4KOZbDRo0CN26dYOjoyNMTU2RmpqK/fv3Izg4uNL1/Pzzz9i3b5/iOU26urq4du0adu/eDT09PcWjBSr78/jpp59i3LhxCAoKwvvvv6+4Dd7Y2BhTp06tsB5ra2vMnDkTK1euRE5ODnx8fGBkZITs7GwcPHgQw4cPx5gxY6px5usPBiCiN9ySJUvg5OSE7du349tvv4W2tjYsLS0xZMgQpSfKzpw5E2KxGNu3b8epU6fg7OyMH374ARMmTKhwH71790Z0dDQiIyPxww8/QBAEtG7dGsOHD1f06d+/P0JCQvDrr79i7969EAThlQHI1tYWsbGxWLlyJdavXw9BEODs7IwVK1aoPAPodWlra2Pjxo1YtGgRVqxYASMjI0ydOhVTpkxR9PHw8MBXX32FDRs2YOnSpbCyssLs2bORk5OjFIC8vb2Rk5OD3bt3Iz8/H2ZmZujWrRumTZummJCrpaWFyMhIbNmyBT///DMOHDgAAwMDWFlZISQkRGnEZOnSpTAzM8O+fftw6NAhdO/eHVFRUZX6DV1fXx/btm1DREQEfvrpJxQWFqJt27ZYtmxZjb6KYsKECfjll19w5swZ7Nu3D0+fPoWFhQUGDRqEyZMnK11SnTJlCm7fvo2NGzfiyZMn6NatGzw8PGBgYIBt27Zh/fr1SExMxJ49e9CoUSO0adNG6dzVpcrWNGTIEOzcuRM//vgjCgoKYGFhgYEDB2LatGmKS48hISE4fPgwjh8/DolEglatWmHmzJlVCghBQUHQ19fHyZMncfjwYRQWFsLMzAw9e/bEhAkT0LFjR0Xfyvw8enp6YuPGjVi1ahVWrVoFHR0duLu7Y86cOSo3BbzK+PHj0aZNG2zZsgWRkZEAns9h69mzJ7y9vSt9bPWVSKipWYZERERE9QTnABEREZHG4SUwIiKiapJIJBXeaWlsbFzuXZykHgxARERE1XT+/HmEhoaW26em521RzeAcICIiomp69OgRLl26VG4fGxsbNGvWrI4qospiACIiIiKNw0nQREREpHE4B4jeeOfPn4cgCNDV1VV3KURE9IYrKSmBSCSCq6truf04AkRvPEEQFH+obgiCAIlEwnNeh3jO1YPnve7V9jmv7OcFR4DojaerqwuJRAIbGxuNeEPxm6CoqAjp6ek853WI51w9eN7rXm2f89TU1Er14wgQERERaRwGICIiItI4DEBERESkcRiAiIiISOMwABEREZHGYQAiIiIijcMARERERBqHAYiIiIg0DgMQERERaRwGICJCfn4+Tp8+jfz8fHWXQkRUJ/gqDCINl5CQgBUrVqCkpAS6urqYM2cOBgwYoO6yiIhqFUeAqN4QiUTqLqHByc/PV4Qf4PlblCMiIjgSREQNHgMQ1QtisRgGBgbqLqPBycjIUIQfOYlEgr///ltNFRER1Q1eAqN6Q5KWAVlRsbrLaFBaF0qgLSmBVCaDSF8PwPOw2b59e9y5c0fN1RER1R4GIKo3ZEXFEAqL1F1Gg2KqpYOZ73+A/8RvhxTPw8/s2bNhamrKAEREDRoDEJGGG9rnbbzdqxeyjfVgY2MDMzMzFBUxaBJRw8Y5QA1MQkIC7O3tcerUKXWXAgBYvXo17O3tkZ2dre5SqBxmJiZwd3eHmZmZukshIqoTDEBERESkcRiAiAgLN62Hu7s75s2bp+5SiIjqBOcAUbWVlpZCIpHw9vR6bk/SEaz7MQaCIODq1avo2rUrH4RIRA0eR4AaKEEQsGXLFrz77rtwcnKCt7c3Nm/erNTn4sWLCA8Px7vvvgsXFxe4uLggMDAQu3fvVtmefC5PZmYmli9fjr59+6JTp0747bffAACFhYX48ssv0atXLzg7O8Pf31+xjN5c+Y8L8N2OWAiCAOD53xs+CJGINAFHgBqob7/9FoWFhQgICIChoSH27NmDf//732jWrBkGDRoEADhw4AAyMjIwYMAAtGrVCo8fP8Zvv/2GefPmIS8vD+PGjVPZ7uzZs6GtrY2RI0fC0NAQbdu2hVQqxbhx43Du3Dm888478PDwwO3btzFv3jy0bdu2rg+dqiAz+xZKpFKlNvmDEA0NDdVUFRFR7WMAaqCKi4vx008/QSwWAwACAwPRt29fbNu2TRGAJk2ahE8++URpvdGjRyMkJATr16/Hhx9+CF1dXaXlhoaGiI6OVmqPj4/HuXPnEBoaivnz5yvafXx88MEHH9TWIVINsLFqDV0d5X8GynoQoqOjIwAgKCgIn3/+eZ3WSEQNm7r+feElsAYqODhYEX6A58HF1dUV169fV2qTe/r0KfLz8/Hw4UP07t0bjx8/VuorN3r0aJVQ9PvvvwMAJk6cqNTu6uoKDw+PGjkeqh1mxiaYGTRK8Z41kUikeBDiyzIzM7Fjx446rpCINIE6/n3hCFAD1bp1a5U2U1NTPHz4UPF1Xl4eVq1ahYMHD+L+/fsq/R89eqTS1qZNG5W2rKwsmJmZoWnTpirLbGxscOLEiaoVT3VqaJ+3seq3n/Hw4UOYmpoiICBA5UGIly5dUvyWRkRUk9T17wsDUAOlpVX+4J4gCBg7diyuXr2K4OBgdOrUCSYmJtDW1sbRo0exZcsWyGQylfX09fVrq2RSkz1JR5CWlgZBEHD79m0kJCTwLjAiavAYgDTUlStXcOnSJUyePBkzZsxQWnb8+PEqbcva2hrXr19Hbm6uyihQZmbma9dKtedVd4F1795dzZUREdUuzgHSUNra2gCg+OCTu3v3LuLj46u0rXfeeQcAsG7dOqX28+fPIyUl5TWqpNomvwuskcHz+WDGxsaKu8CIiBoyjgBpqHbt2sHOzg4bN25EUVERbG1tkZ2dje3bt6N169ZKc4Uq4u/vj927d2Pr1q34559/0KNHD9y5cwexsbHo2LEjLl26VHsHQq9FfhdYy6ZNITK0AlD2XWBERA0NR4A0lLa2NtavX4/+/fvjl19+wRdffIEjR45gzpw5GDlyZJW2paOjg40bNyI4OBjnzp3Dv//9byQnJ2Pp0qXo27dvLR0B1QT5XWC6Os/v7BOLxa+8C4yIqCERCS9fAyF6w6SmpgIAbJ8JEAqLKuhN1fFQJkW2sR5sbGxgZmaGoqIipKenw8HBQfG4BPldGhzRqx1lnXOqfTzvda+2/32Rf2Z06tSp3H68BEZEMDMxQcuuzuoug4iozjAAUb2hZWgA1RvzqSZoGfKFtkSkWRiAqN4Qd7RVdwkNmiAIiidCExE1dJwETfWCRCJBcXGxusto0Bh+iEiTMABRvcH5+kREVFMYgIiIiEjjMAARERGRxmEAIiIiIo3DAEREREQahwGIiIiINA4DEBEREWkcBiAiIiLSOAxAREREpHEYgIiIiEjjMAARERGRxmEAIiIiIo3DAEREREQaR0fdBRBR/XD//n3FC2mXLFkCQRCwcOFCNVdFRFQ9HAEiokoxMjJCbm4ujh07huXLl+Pdd99Vd0lERNXGAEREFUpKSoIgCNDX10f//v3h6uoKiUSCpKQkdZdGRFQtvARGRBUKCwvD7du3UVpaCkEQcOnSJYSFhUEkEuHvv/9Wd3lERFXGAEREFbp+/To6duyIv//+G507d8bTp09x/vx5aGlxEJmI6if+60VElZKfnw9BEPDHH3/g6dOnWL16tbpLIiKqNgYgIqq0Jk2awNDQEB9//DFHf4ioXuO/YERUKbq6urh37x6uXLmCefPmoV27duouiYio2hiAiKhCWVlZ0NfXh76+Pnr27AlHR0d06tQJWVlZ6i6NiKhaOAmaiCrUpk0bxUMQi4uLcfz4cbRp0wYikQilpaVqro6IqOo4AkREFZLJZOjYsSMMDQ3Rtm1b9OrVCzKZjOGHiOotBiAiqpRHjx5BIpEgJSUFmZmZiImJUXdJRETVxgBERJXWvHlzNGvWDKtWrVJcEiMiqo84B4iIKqVx48aK/w8MDFRjJUREr48jQERERKRxGICIiIhI4zAAERERkcZhACIiIiKNwwBEREREGocBiIiIiDQOAxARERFpHAYgIiIi0jgMQERERKRxGICIiIhI4zAAERERkcZhACIiIiKNwwBEREREGocBiIiIiDQOAxARERFpHAYgIiIi0jgMQERERKRxGICo3hCJROougYiIGggGIKoXxGIxDAwM1F1GvScIgrpLICJ6I+iouwCiypKkZUBWVKzuMuotLUMDiDvaqrsMIqI3AgMQ1RuyomIIhUXqLqPekqm7ACKiNwgvgREREZHGYQCi15aeno6wsDC4u7vD3t4eq1evVndJVI78/HycPn0a+fn56i6FiEhteAmMXotUKsW0adMglUoxY8YMGBsbw97eHqdOncLp06cRFhYGExMTdZdJ/19CQgJWrFiBkpIS6OrqYs6cOQgICFB3WUREdY4jQPRabt26hVu3biEsLAzBwcHw8/NDhw4dcPr0aaxZswYFBQXqLlHjJaelYtamtfgt5ThWrFiBvLw8ZGVlIS8vDxERERwJIiKNxBEgei0PHjwAADRu3FjNldCrxBw5hOv/3EbqlSt4WiJBbm4uJJLn/zU2NkZmZibc3d3VXSYRUZ1iANIwEokEGzZswK+//orbt29DS0sLFhYWcHNzw8KFC6Gvrw8ASEpKwoYNG3Dp0iVIpVK0bdsWw4cPx8iRIxUPJPT29kZOTg4AIDw8HOHh4QAAS0tLRXu/fv0U+546dSqmTZtWl4dLADJuXMf/LqdDkMkgfwqQlpYWZDIZxGIxbG15azwRaR4GIA2zZMkS7Nq1C4MHD0ZISAgAIDs7G0eOHEFRURH09fWxa9cuLFiwAK1atcKYMWNgZGSExMRELFmyBJcvX8YXX3wBAJg3bx5SU1Oxbt06BAUFoUuXLgAAIyMj7NmzBwcOHEB4eDjMzMwAAPb29uo5aA2W/7gAqWmXUFRcDJFIBB0dHZSUlMDQ0BBaWlqYPXs2TE1N1V0mEVGdYwDSML///jt69+6NiIgIpfY5c+YAAB4/foylS5fC3Nwc8fHxaNKkCQAgODgY48ePx86dO+Hn54euXbvCx8cHxsbGWLduHVxcXODn56fYXnp6Og4cOAAfHx9YWVnV3QGSkvNXL6Oo+PnDIwVBgFQqhSAIaNWqFZo3b17pCdCOjo7IzMyEjY1NbZZLRFRnOAlaw8jnfFy+fLnM5cnJySgqKkJISIgi/ACAjo4OJk2aBOB5iKL6SRAEiEQiGBoaQldXt0rr2tjYICgoqJYqIyKqWxwB0jDz58/Hp59+Cj8/P7Rq1QpdunRBr169MHDgQOjp6eHWrVsAADs7O5V15W1ZWVl1WjNVn6tdB+jp6eHZs2cAnr9QViwWw9jYuErbuXTpUm2UR0SkNgxAGsbb2xuHDx9GcnKy4lk9+/btQ2RkJHbs2KHu8qiGmRmbwMWpE/53OR0QBHRwcICuri50dXVhbm6u7vKIiNSGl8A0kImJCXx9fbF48WL89ttvWLRoEbKyshAbGwtra2sAQEZGhsp68jZ5n/LI7xQj9bNt0xYuLi7o3cMD+/fvV4zkGRoaqrkyIiL1YQDSIKWlpXj06JFKu6OjIwDg4cOH6NmzJwwNDREbG6vUt7S0FN9//z0AoH///hXuS/7hWtb+qG4Fv+0Dx7faYtYHwTAzM8OHH36IDh064MMPP1R3aUREasNLYBrkyZMn6NWrF/r27QsHBwdYWFjg3r172LVrF3R0dDB48GAYGxtj3rx5WLBgAQIDAxEYGAgDAwPs378f586dw/Dhw9G1a9cK99W5c2cAQEREBAYPHgw9PT3Y2tqWObeIalevjk7o1dEJokbPQ6mXlxe8vLzUXBURkXoxAGkQfX19fPTRRzh58iROnz6NwsJCNG3aFJ07d8bYsWPh7OwMABg2bBiaNWuGjRs3IioqSvEgxAULFmDUqFGV2leXLl0we/ZsbN++HQsWLIBUKsXUqVMZgIiI6I3AAKRBxGIxPv7440r1rewoQffu3XHlypUyl40bNw7jxo2rUo1ERER1gXOAiIiISONwBIjqDS1DA8jUXUQ9pmVooO4SiIjeGAxAVG+IO/Klna9L/iRoIiJNx0tgVC9IJBIU//93WlH1MfwQET3HAET1hiAI6i6BiIgaCAYgIiIi0jgMQERERKRxGICIiIhI4zAAERERkcZhACIiIiKNwwBEREREGocBiIiIiDQOAxARERFpHAYgIiIi0jgMQERERKRxGICIiIhI4zAAERERkcZhACIiIiKNwwBEREREGocBiIiIiDQOAxARERFpHAYgIiIi0jgMQERERKRxGICIiIhI4zAAERERkcZhACIiIiKNwwBEREREGocBiIiIiDQOAxARERFpHAYgIiIi0jgMQERERKRxGICIiIhI4zAAERERkcZhACIiIiKNwwBEREREGocBiIiIiDQOAxARERFpHAYgIiIi0jgMQERERKRxGICIiIhI4zAAERERkcZhACIiIiKNwwBEREREGocBiIiIiDQOAxARERFpnDciAKWnpyMsLAzu7u6wt7fH6tWr1V1SnUlISIC9vT1OnTpVL7dfGSEhIfD29lbb/huK/Px8nD59Gvn5+eouhYio3tNRdwFSqRTTpk2DVCrFjBkzYGxsrPjAPn36NMLCwmBiYqLuMonUKiEhAStWrEBJSQl0dXUxZ84cBAQEqLssIqJ6S+0jQLdu3cKtW7cQFhaG4OBg+Pn5oUOHDjh9+jTWrFmDgoICdZdYr/n5+eHixYtwd3dXdylUTfn5+YrwAwAlJSWIiIjgSBAR0WtQewB68OABAKBx48Z1sr/CwsI62Y+6yY9TW1sbenp60NJS+7f6tYlEInWXoBYZGRmK8CMnkUiQmZmppoqIiOq/Kn8qSiQSREZGwtfXFy4uLnBzc8O7776L8PBwPH36VNEvKSkJISEhcHNzg7OzM/z8/BAbGwtBEBR9vL29ERwcDAAIDw+Hvb097O3t4e3tjTVr1gAA+vXrp2ivytyg7OxsxTr79+/H+++/j86dO2PSpEmKPmlpaZg+fTo8PDzg5OSEfv36ISIiAsXFxSrbu3jxIkJDQ+Hi4gJ3d3fMmDEDt2/fhre3N0JCQsrc78sqOx+nsLAQ3333HYYPH44ePXrAyckJ3t7eWLJkCR4+fFil4yxrn/LzWdafl+fqZGVlYe7cuejVqxecnJzQp08fLFq0CHl5eSp137x5E1OmTEGXLl3g6uqK0aNH4/Lly+Uea2WJxWIYGBjUyLbqG1tbW+jq6iq1icVi2NraVmr9F3/miIjouSrPAVqyZAl27dqFwYMHKz74s7OzceTIERQVFUFfXx+7du3CggUL0KpVK4wZMwZGRkZITEzEkiVLcPnyZXzxxRcAgHnz5iE1NRXr1q1DUFAQunTpAgAwMjLCnj17cODAAYSHh8PMzAzA8w/uqjp06BCio6MxYsQIDB8+XPFhkJSUhClTpqBly5YIDg6Gubk5Ll++jC1btuDcuXPYunUrdHSen56LFy8iJCQEurq6CA0NRfPmzXHy5EkEBwejqKioyjVV5O7du9i5cyfeeecd+Pr6QiwWIzU1FTt27MDZs2cRHx+v8oH4quMsy/Lly1XaMjMzERUVBXNzc0Vbeno6QkJCoK+vj8DAQFhaWuLGjRuIi4tDSkoK4uPjYWxsDAC4c+cORowYgcePH2PEiBFo164dLly4gJCQEJiamtbIeZGkZUBWpBpOGzoDADP8h+G7mK0okZZAV0cX04ePhH5mFp4iq9x1tQwNIO5YuaBERKRJqhyAfv/9d/Tu3RsRERFK7XPmzAEAPH78GEuXLoW5uTni4+PRpEkTAEBwcDDGjx+PnTt3ws/PD127doWPjw+MjY2xbt06uLi4wM/PT7G99PR0HDhwAD4+PrCysqr2AWZkZOCnn36CnZ2dou3Zs2eYN28eOnTogNjYWIjFYsWyHj16YPr06di3bx/8/f0BAEuXLkVJSQl27typCGGjRo3Cl19+iW3btlW7tldp3bo1jh49qhJy3Nzc8K9//QsHDx7EwIEDKzzOV3nxPAPPA9e3334Lc3NzrFy5UtEeHh6Oxo0bY/fu3UohZsCAARgxYgSio6MxdepUAMC3336LvLw8REZGwsfHBwAwcuRIREVFYeXKlbC0tKzSOSiLrKgYQmHNB876wK+bJ/o4OOFaTjbaW1rBzNikUudCVge1ERHVR1W+BGZsbIzMzMxXXtpITk5GUVERQkJCFOEHAHR0dBSXZX7//fdqllt1Xl5eKqHgxIkTuH//Pvz9/VFYWIi8vDzFH3d3dxgYGCA5ORkAkJubi/Pnz6NPnz4qI1Djx4+vlZrFYrEi/EilUhQUFCAvLw89evQA8HxE6mVlHWdlFBYWYty4cXj06BHWrVuH1q1bAwCuXr2K9PR0DBo0CDKZTOkcWVlZwdraWnGOZDIZDh48CBsbG0X4kQsLC4OhoWGV6yJVZsYm6NqhI8yMeVckETUs9+/fx7179wA8v9K0ePHiWt9nlUeA5s+fj08//RR+fn5o1aoVunTpgl69emHgwIHQ09PDrVu3AKDMD2N5W1ZW+cP2NalNmzYqbdeuXQMALF68+JUnWT45W348bdu2VenTrFkzxSWgmrZz5078+OOPyMjIgFQqVVr28jwgoOzjrEhJSQmmTp2KzMxMrF27Fp06dVIsk5+j9evXY/369WWuLw9Lubm5ePLkCdq3b6/SR09PD9bW1nj8+HGV6yMiIs1gZGSEmzdv4tixY1i+fDkOHjxY6/uscgDy9vbG4cOHkZycrHhWz759+xAZGYkdO3bURo2vpayJszLZ8wsDs2bNgrOzc5nrVffZQ+XdqVRaWlqpbURHR2Pp0qXw9PTEwoUL0axZM4jFYkilUowbN67M+T1VnSAsCALmzZuHlJQULFmyBG+//bbKcqD8hxjq6elVaZ9EREQvS0pKgiAI0NfXR//+/dG1a1dIJBIkJSWhT58+tbbfaj0I0cTEBL6+vvD19QUAxMXFYdGiRYiNjVXcmZKRkYG+ffsqrZeRkQEAsLa2rnAftXnLs3w0R09PD56enuX2lY9yXL9+XWXZvXv3VEY25LfzP3r0SKW/fDSpInv27IGlpSU2bdqkdPu6fFSmJnzzzTfYu3cvJk6ciKCgIJXlL44oVXSOmjZtCiMjozLre/bsGbKyshQT2en1JaelYnfKMQR69EGvjk41uu2jR49ix44d8Pf3V7qETURUW8LCwnD79m2UlpZCEARcunQJYWFhEIlE+Pvvv2ttv1WaA1RaWlrmB7ujoyOA55dmevbsCUNDQ8TGxir1LS0txffffw8A6N+/f4X7ks8bKWt/r6tXr14wNzfHpk2bcP/+fZXlUqlUcZmpadOmcHV1RVJSEq5cuaLULyoqSmXdRo0awcLCAidPnlQaqcnPz8fu3bsrVZ889MhHqoDnIzKRkZGVWr8icXFxiIqKwpAhQzBr1qwy+zg4OMDOzg7x8fFlBhtBEBS3wmtpaaFfv37IzMxUGbaMjo6ulTvlNFnMkUPIuJ2DmCM1P0S8ZcsWXLlyBTExMTW+bSKisly/fh02NjbQ1dWFu7s7WrdujWvXrtVq+AGqOAL05MkT9OrVC3379oWDgwMsLCxw79497Nq1Czo6Ohg8eDCMjY0xb948LFiwAIGBgQgMDISBgQH279+Pc+fOYfjw4ejatWuF++rcuTMAICIiAoMHD4aenh5sbW2rNdH3ZQYGBli+fDkmT54MX19fBAQEoF27dnjy5AmysrJw4MABfPLJJ4pXDYSHhyMkJASjRo3CyJEjFbfBp6WllTmyERoaipUrV2LMmDHw8fFBXl4edu3aBSsrK8XcovIMGDAAERERGDNmDN599108ffoUBw4cUHkYXnVcvXoVX3zxBUxNTdG9e3f8/PPPSsuNjIzg4+MDkUiEFStWICwsDP7+/vD394ednR2kUilycnJw8OBB+Pv7Y9q0aQCAmTNn4tixY5g5c6bSbfCHDx+GtbV1pS//UcWKJc+U/luT5GG1rGdhERHVlvz8fAiCgD/++AOurq5YvXo1ZsyYUav7rFIA0tfXx0cffYSTJ0/i9OnTKCwsRNOmTdG5c2eMHTtWMZ9m2LBhaNasGTZu3IioqChIpVK0bdsWCxYswKhRoyq1ry5dumD27NnYvn07FixYAKlUiqlTp9ZIAAKAnj17IiEhARs2bEBiYiJyc3PRqFEjtGrVCoGBgfDw8FD07dy5M7Zu3YqVK1ciOjoaYrEYnp6eiImJwdChQ6Gvr6+07TFjxuDJkydISEjA6dOn0aZNG8U38q+//qqwtjFjxgAA4uPjsWzZMpiamqJfv36YNWsWunXr9lrHnZ+fj9LSUjx8+BDz589XWW5paam4k6tDhw74+eefERUVhaSkJOzevRsGBgZo0aIF+vXrp3QrvqWlJeLi4rBixQokJCRAEAS4urpi69atWLp0KXJycl6rbiIiatiaNGkCQ0NDfPzxx5BIJLW+P5HAx8RWW15eHjw8PDBixIg6uWVPU6WmpgIAbJ8JGvscoBe998X/BdemFd0SLxJBJNZ95WKJRILHjx/D2NgYYrFYaYRy+fLlcHBw4GMM6khRURHS09N5zusYz3vdK+ucW1tbIycnB2lpafD09MTWrVsxaNCgam1f/pnx4p3NZVH72+Dri6dPn6qM9KxduxYA0Lt3b3WURITcx9V/WfC9e/eQlZUFQRAgEolgbW2NZs2a1WB1REQVy8rKgr6+PvT19dGzZ084OjqiU6dOyMrKqtRNU9VVrwJQaWlpme+gepmxsbFKWHkdUqkUXl5eeO+999C+fXsUFxfj+PHjOH78OLp37/7K28SJalt1R4AkEgkuXLgAbW1tRdudO3dgZmam8gRyIqLa1KZNG8VNQ/LP1zZt2kAkEtXq/NF6FYDu3LmDfv36Vdhv2bJlignMNUFbWxs+Pj5ITk5GQkICpFIpLC0tMWnSJEycOLFBvGmd6o+mxibIfVyApsYmiJ75Wbl9RY0Mod9V9VlXp0+fLvOxDHp6epDJZGjatGmN1UtEVB6ZTAZHR0fcuHEDLVq0gJWVFY4ePVrr+61XAcjCwgKbN2+usJ+NjU2N7lckEuGrr76q0W0SVZeBWE/pv9Uhf8P8i3cWisViWFhY4O7du1V+sCYR0et49OgRJBIJUlJS4OrqipiYGAQHB9fqPutVAKrMgwuJGrrgt32QkHIMAR7Vn3tmZmaGOXPmICIiAhKJBGKxGLNnz0bTpk2xc+dODB06tOYKJiKqhObNm6NZs2ZYtWpVnTw/rl4FoDfFqVOnEBoaWuOX2ogqo1dHpxp5AnRAQAD69u2LzMxM2NjYKJ5p5eXlpbhLg4ioLsjfogAAgYGBdbJPBqB66tGjR9i7dy+SkpKQmZmJBw8ewMLCAk5OTpg4cSI6duxYpe2dOXMGq1evVrp9cPr06ZV6aKXcrVu3sH79epw6dQp3796FiYkJ7O3tERoaCi8vryrVQ3XDzMwM7u7u6i6DiKjOcfZuPXXhwgUsXboUUqkUI0aMwOeffw5fX1+cOHECAQEB+PXXXyu9rWPHjuHDDz9EdnY2pk6dio8//hj5+fkICwvDiRMnKrWN7OxsBAQEYP/+/ejfvz8WLFiA4OBg3Lx5E+PHj8euXbuqe6hUw/Lz83H69Gnk5+eruxQiIrXhCFA91a5dOyQmJuKtt95Sah8yZAgCAgKwdOlSDBw4sMI71EpLS7Fw4UKIxWLExMSgVatWAIChQ4di0KBBWLRoERITEyvczq5du1BQUIB169YpvQR32LBh6NOnD7Zv345hw4ZV82ippiQkJGDFihUoKSmBrq4u5syZw8u4RKSRGIBqgEwmQ1RUFI4fP47r16/j4cOHMDMzg6enJ2bMmKEIFXL29vbw9/dHYGAgvvnmG6Snp8PQ0BD+/v74+OOPUVpailWrVmHfvn3Iy8uDnZ0dFixYABcXF8U2rKysyqzFzs4Otra2SEtLQ25uLiwsLBTLsrKyUFJSgvbt2yva/vzzT+Tk5CAgIECpTmNjYwwbNgxr1qzBuXPnKrwUVlhYCAAqD9IzNTWFWCyukbuKtAwNIKu4G71Ay/D/znt+fr4i/ABASUkJIiIi0Ldv3zLfaUdE1JAxANWAkpISbNiwAf3794eXlxeMjY1x5coV7N69GykpKdi7dy9MTU2V1klLS8Phw4cRGBiIIUOG4OjRo9i4cSO0tbVx9epVFBYWYvTo0SguLsbmzZsxYcIEHDp0CI0aNSq3FplMhvv370NXVxcmJsoPyfvwww+Rk5Oj9Fb7CxcuAABcXV1VtiVvS01NrTAA9erVCzExMVi4cCHmzJmDNm3a4MGDB9i4cSNKS0sxefLkctevDHFH29fehiaSP+k5IyND5YW6EokEmZmZnAdERBqHAagGiMViJCcnq4xy+Pj44KOPPkJ8fDzGjh2rtOzq1auIi4tThIwPPvgAQ4cORVRUFLy8vLBt2zaIRCIAz59rNGXKFPz6668ICgoqt5bY2Fjcv38f/v7+0NOr+Dkxd+/eBfD89sOXtWjRAgDwzz//VLidvn37Ijw8HN9//z1CQ0MV7S1btkRMTIziRbnVJZFIUFxczOfTVIP879Grnv1ja8tgSUSah5Oga4BIJFJ8MMtkMhQUFCAvLw8dOnSAsbExLl68qLKOi4uLyqhL165dIQgCQkNDFR9aABS/nd+4caPcOk6dOoWvv/4aVlZWmDt3rsryw4cPK43+AM8fOw48/yB8mTxAyftUxMLCAjY2Npg2bRoiIyPx+eefQ0dHB+PHj0daWlqltlEevrf39cif/SP/Xsuf/fPy6CQRkSbgCFANOXjwIDZu3Ij//e9/KpcZHj58qNK/devWKm3y5yC8vEzeXtZ25M6ePYuJEyeiSZMm+OGHHyr9oSYPbhKJRGXZs2fPlPqUJy4uDosWLcKGDRvQp08fRXv//v0xcOBAfP7554iPj69UTVR7XvXsHyIiTcMAVAMOHjyIKVOmwMnJCeHh4WjZsqXiZayzZs0qc+TixZdQvuxVd1y9agTkzJkzGD9+PBo3bozo6GiVO8PKI7/0Jb8U9iL5pS/5pbDybNy4EUZGRkrhB3g+KtS1a1f88ccfKCoqgqGhYaVro9rBZ/8QETEA1Yg9e/ZAT08PMTExSqMlRUVFKCgoqNV9nzx5UjHyEx0dXebIUnnkc3POnz+P4cOHKy3766+/ADx/KGJF7t69C11dXcWE2xdJpVIAqNW3+hIREVUF5wDVAC0tLYhEIshkyjdpr127VqWtJqWkpGDChAmwsLBATExMheEnKysL165dU2pzd3eHpaUlEhMTcefOHUV7YWEhdu3ahdatW8PNzU3RXlJSgmvXruH27dtK27G1tUVRURF+++03pfZbt27hzz//RNu2bWFsbFzdQyUiIqpRHAGqAQMGDMD+/fsREhICf39/CIKA5ORkZGZm1toci9TUVEycOBFSqRTDhg3DmTNnVPq88847SpecyroNXltbGwsXLsSkSZMwatQohISEQFdXFzt27MCDBw+wfv16pUtyd+/eha+vL7p164Zt27Yp2qdNm4YpU6bg008/xenTp+Hg4IB//vkHcXFxePbsGWbPnl0r54GIiKg6GIBqgK+vL4qKihAdHY0VK1bAyMgInp6e+PHHHzFy5Mha2WdGRgaePn0KAFi5cmWZfQ4dOlSpOTdeXl744YcfEBkZiVWrVgEAnJycsHnzZnTr1q1S9Xh7e2Pr1q3YtGkTEhMTsXPnTjRq1AidO3fGuHHjKr0dIiKiuiASeG8xveFSU1MhkUjg4ODASdR1RP42eJ7zusNzrh4873WvrHPu6OgIALh06dJrb//Fl3qXh3OAiIiISOMwABEREZHGYQAiIiIijcMARERERBqHAYiIiIg0DgMQERERaRwGICIiItI4DEBERESkcRiAiIiISOMwABEREZHGYQAiIiIijcMARERERBqHAYiIiIg0DgMQERERaRwGICIiItI4DEBERESkcRiAiIiISOMwABEREZHGYQAiIiIijcMARERERBqHAYiIiIg0DgMQERERaRwGICIiItI4DEBERESkcRiAiIiISOMwABEREZHGYQAiIiIijcMARERERBqHAYiIiIg0DgMQERERaRwGICIiItI4DEBERESkcRiAiIiISOO8EQEoPT0dYWFhcHd3h729PVavXq3ukupMQkIC7O3tcerUqXq5/coICQmBt7e32vZPRET0Mh11FyCVSjFt2jRIpVLMmDEDxsbGig/s06dPIywsDCYmJuouk4iIiBoQtY8A3bp1C7du3UJYWBiCg4Ph5+eHDh064PTp01izZg0KCgrUXWK95ufnh4sXL8Ld3V3dpRAREb0x1B6AHjx4AABo3LhxneyvsLCwTvajbvLj1NbWhp6eHrS01P6tJiIiemNU+VNRIpEgMjISvr6+cHFxgZubG959912Eh4fj6dOnin5JSUkICQmBm5sbnJ2d4efnh9jYWAiCoOjj7e2N4OBgAEB4eDjs7e1hb28Pb29vrFmzBgDQr18/RXtV5gZlZ2cr1tm/fz/ef/99dO7cGZMmTVL0SUtLw/Tp0+Hh4QEnJyf069cPERERKC4uVtnexYsXERoaChcXF7i7u2PGjBm4ffs2vL29ERISUuZ+X1bZ+TiFhYX47rvvMHz4cPTo0QNOTk7w9vbGkiVL8PDhwyodZ1n7lJ/Psv68PFcnKysLc+fORa9eveDk5IQ+ffpg0aJFyMvLU6n75s2bmDJlCrp06QJXV1eMHj0aly9fLvdYq0IkEtXYtoiISLNVeQ7QkiVLsGvXLgwePFjxwZ+dnY0jR46gqKgI+vr62LVrFxYsWIBWrVphzJgxMDIyQmJiIpYsWYLLly/jiy++AADMmzcPqampWLduHYKCgtClSxcAgJGREfbs2YMDBw4gPDwcZmZmAJ5/cFfVoUOHEB0djREjRmD48OGKAJaUlIQpU6agZcuWCA4Ohrm5OS5fvowtW7bg3Llz2Lp1K3R0np+eixcvIiQkBLq6uggNDUXz5s1x8uRJBAcHo6ioqMo1VeTu3bvYuXMn3nnnHfj6+kIsFiM1NRU7duzA2bNnER8fD11d3UodZ1mWL1+u0paZmYmoqCiYm5sr2tLT0xESEgJ9fX0EBgbC0tISN27cQFxcHFJSUhAfHw9jY2MAwJ07dzBixAg8fvwYI0aMQLt27XDhwgWEhITA1NT0tc+JWCyGgYHBa2+nPhEEgaGPiKiWVDkA/f777+jduzciIiKU2ufMmQMAePz4MZYuXQpzc3PEx8ejSZMmAIDg4GCMHz8eO3fuhJ+fH7p27QofHx8YGxtj3bp1cHFxgZ+fn2J76enpOHDgAHx8fGBlZVXtA8zIyMBPP/0EOzs7RduzZ88wb948dOjQAbGxsRCLxYplPXr0wPTp07Fv3z74+/sDAJYuXYqSkhLs3LlTEcJGjRqFL7/8Etu2bat2ba/SunVrHD16VCXkuLm54V//+hcOHjyIgQMHVnicr/LieQaeB65vv/0W5ubmWLlypaI9PDwcjRs3xu7du5VCzIABAzBixAhER0dj6tSpAIBvv/0WeXl5iIyMhI+PDwBg5MiRiIqKwsqVK2FpaVmlc1AWSVoGZEWqo3MNkZahAcQdbdVdBhFRg1XlS2DGxsbIzMx85aWN5ORkFBUVISQkRBF+AEBHR0dxWeb333+vZrlV5+XlpRIKTpw4gfv378Pf3x+FhYXIy8tT/HF3d4eBgQGSk5MBALm5uTh//jz69OmjMgI1fvz4WqlZLBYrwo9UKkVBQQHy8vLQo0cPAM9HpF5W1nFWRmFhIcaNG4dHjx5h3bp1aN26NQDg6tWrSE9Px6BBgyCTyZTOkZWVFaytrRXnSCaT4eDBg7CxsVGEH7mwsDAYGhpWua6yyIqKIRQWacQfTQl6RETqUuURoPnz5+PTTz+Fn58fWrVqhS5duqBXr14YOHAg9PT0cOvWLQAo88NY3paVlfWaZVdemzZtVNquXbsGAFi8eDEWL15c5nryydny42nbtq1Kn2bNmikuAdW0nTt34scff0RGRgakUqnSspfnAQFlH2dFSkpKMHXqVGRmZmLt2rXo1KmTYpn8HK1fvx7r168vc315WMrNzcWTJ0/Qvn17lT56enqwtrbG48ePq1wfERFRbalyAPL29sbhw4eRnJyseFbPvn37EBkZiR07dtRGja+lrHkjMpkMADBr1iw4OzuXuV51nz1U3pyN0tLSSm0jOjoaS5cuhaenJxYuXIhmzZpBLBZDKpVi3LhxZc7vqer8GEEQMG/ePKSkpGDJkiV4++23VZYD5T/EUE9Pr0r7JCIielNU60GIJiYm8PX1ha+vLwAgLi4OixYtQmxsLGxtn89byMjIQN++fZXWy8jIAABYW1tXuI/anPwpH83R09ODp6dnuX3loxzXr19XWXbv3j2VkQ357fyPHj1S6S8fTarInj17YGlpiU2bNindvi4flakJ33zzDfbu3YuJEyciKChIZfmLI0oVnaOmTZvCyMiozPqePXuGrKwsxUT2hiw5LRW7U44h0KMPenV0Unc5AICjR49ix44dCAoKgpeXl7rLISJ6Y1RpDlBpaWmZH+yOjo4Anl+a6dmzJwwNDREbG6vUt7S0FN9//z0AoH///hXuSz5vpKz9va5evXrB3NwcmzZtwv3791WWS6VSxWWmpk2bwtXVFUlJSbhy5YpSv6ioKJV1GzVqBAsLC5w8eVJppCY/Px+7d++uVH3y0CMfqQKej8hERkZWav2KxMXFISoqCkOGDMGsWbPK7OPg4AA7OzvEx8eXGWwEQVDcCq+lpYV+/fohMzMTBw8eVOoXHR1dK3fKvYlijhxCxu0cxBw5WHHnOrJlyxZcuXIFW7ZsUXcpRERvlCqNAD158gS9evVC37594eDgAAsLC9y7dw+7du2Cjo4OBg8eDGNjY8ybNw8LFixAYGAgAgMDYWBggP379+PcuXMYPnw4unbtWuG+OnfuDACIiIjA4MGDoaenB1tb22pN9H2ZgYEBli9fjsmTJ8PX1xcBAQFo164dnjx5gqysLBw4cACffPIJAgICADy/GyokJASjRo3CyJEjFbfBp6WllTmyERoaipUrV2LMmDHw8fFBXl4edu3aBSsrK8XcovIMGDAAERERGDNmDN599108ffoUBw4cQElJyWsf+9WrV/HFF1/A1NQU3bt3x88//6y03MjICD4+PhCJRFixYgXCwsLg7+8Pf39/2NnZQSqVIicnBwcPHoS/vz+mTZsGAJg5cyaOHTuGmTNnKt0Gf/jwYVhbW1f68l99lf+4ADl3/0GJIEOx5Jm6y1GQh09NCaFERJVVpQCkr6+Pjz76CCdPnsTp06dRWFiIpk2bonPnzhg7dqxiPs2wYcPQrFkzbNy4EVFRUZBKpWjbti0WLFiAUaNGVWpfXbp0wezZs7F9+3YsWLAAUqkUU6dOrZEABAA9e/ZEQkICNmzYgMTEROTm5qJRo0Zo1aoVAgMD4eHhoejbuXNnbN26FStXrkR0dDTEYjE8PT0RExODoUOHQl9fX2nbY8aMwZMnT5CQkIDTp0+jTZs2mDFjBgDgr7/+qrC2MWPGAADi4+OxbNkymJqaol+/fpg1axa6dev2Wsedn5+P0tJSPHz4EPPnz1dZbmlpqbiTq0OHDvj5558RFRWFpKQk7N69GwYGBmjRogX69eundCu+paUl4uLisGLFCiQkJEAQBLi6umLr1q1YunQpcnJyXqvuN9mepCP4bkcsMnKyUSrIILXvoO6SiIioAiKhvCfmUbny8vLg4eGBESNGvPJuMnp9qampAADbZwKEwjdrJCP/cQH8536CEqkUmXeehzyRSIS+PXspPV+qykQiiMS6FferwIsjjocOHar0ekVFRUhPT4eDg0ONPcaAysdzrh4873WvrHMun0pz6dKl196+/DPjxTuby6L2t8HXF0+fPlUZ6Vm7di0AoHfv3uooid4Amdm3UPLSYwoEQUDO/XvVvpOQiIhqX70KQKWlpWW+g+plxsbGKmHldUilUnh5eeG9995D+/btUVxcjOPHj+P48ePo3r37K28Tp4bPxqo1dHV0lEKQSCSCZbNmEOu+WSNARET0f+pVALpz5w769etXYb9ly5YpJjDXBG1tbfj4+CA5ORkJCQmQSqWwtLTEpEmTMHHiRL5pXYOZGZtgZtAo/GdnLHS0tFEqyOBo3wFxcxa81nZFjQyh37XsZ1RVRVBQEB48eKD0jjciIqpnAcjCwgKbN2+usJ+NjU2N7lckEuGrr76q0W1SwzG0z9vwcnXDuP+swGPJM7Rt0UrdJSnIr69zbgMRkbJ6FYAq8+BCInUwMzbBzIAgJKQcQ4BHzc8Jy8/PR0ZGBmxtbav0UMkPP/wQO3fuxPDhw2u8JiKi+qxeBSCiN1mvjk618gTohIQErFixAiUlJdDV1cWcOXMqfYnXy8uLT4AmIioDJ69Uw6lTp2Bvb4+EhAR1l0INXH5+viL8AM9fYBsREYH8/Hw1V0ZEVL9xBKieevToEfbu3YukpCRkZmbiwYMHsLCwgJOTEyZOnIiOHTtWaXtnzpzB6tWrlZ6fMH369Eo9tRsALl68iI0bN+Ly5ct48OABZDIZWrZsiT59+mD06NFo3rx5lY+Rnr8/7+UngEskEmRmZsLd3V1NVRER1X8MQPXUhQsXsHTpUvTo0QMjRoxAkyZNcPPmTWzfvh2///47Vq5ciUGDBlVqW8eOHcPEiRPRvHlzTJ06FWKxGDt37kRYWBg2bNhQqXlXN27cwJMnTzBo0CBYWFhAS0sLV65cwY4dO/DLL78gISGBIagabG1toaurqxSCxGKx4qXDRERUPQxA9VS7du2QmJiIt956S6l9yJAhCAgIwNKlSzFw4MAKb9EvLS3FwoULIRaLERMTg1atnt/BNHToUAwaNAiLFi1CYmJihdsZMmQIhgwZotLu7u6OWbNmYdeuXZg6dWoVj1KZlqEBZBV3axC0DA0AAGZmZpgzZw4iIiIgkUggFosxe/ZsmJqaqrdAIqJ6jgGoBshkMkRFReH48eO4fv06Hj58CDMzM3h6emLGjBmKUCFnb28Pf39/BAYG4ptvvkF6ejoMDQ3h7++Pjz/+GKWlpVi1ahX27duHvLw82NnZYcGCBXBxcVFsw8rKqsxa7OzsYGtri7S0NOTm5sLCwkKxLCsrCyUlJWjfvr2i7c8//0ROTg4CAgKU6jQ2NsawYcOwZs0anDt3rtKXwl4mr/Px48fVWv9F4o6aNeohCAJEIhECAgLQt29fZGZmwsbGpkp3gRERUdkYgGpASUkJNmzYgP79+8PLywvGxsa4cuUKdu/ejZSUFOzdu1flN/a0tDQcPnwYgYGBGDJkCI4ePYqNGzdCW1sbV69eRWFhIUaPHo3i4mJs3rwZEyZMwKFDh9CoUaNya5HJZLh//z50dXVVXsXw4YcfIicnB1euXFG0XbhwAQDg6uqqsi15W2pqaqUDUHFxMYqLi/Hs2TNkZmZi5cqVAIC33367Uuu/ikQiQXFxMQwMDF5rO/WJSCRS/L+ZmRnn/BAR1SAGoBogFouRnJys8uHs4+ODjz76CPHx8Rg7dqzSsqtXryIuLk4RMj744AMMHToUUVFR8PLywrZt2xQfgDY2NpgyZQp+/fVXBAUFlVtLbGws7t+/D39/f+jp6VVY+927dwGgzPk5LVq0AAD8888/FW5HbuPGjVizZo3iaysrK3z99dfw8PCo9DZehe/tJSKimsIAVANEIpEi/MhkMhQWFkIqlaJDhw4wNjbGxYsXVdZxcXFRGXXp2rUr0tPTERoaqvTbv/w3/xs3bpRbx6lTp/D111/DysoKc+fOVVl++PBhlbbi4mIAKPPN5fIAJe9TGUOHDkWXLl1QWFiIS5cu4fDhw3j48GGl16eaUd0HJxIRaQoGoBpy8OBBbNy4Ef/73/9UblsuKwC0bt1apa1x48ZlLpO3lxckzp49i4kTJ6JJkyb44YcfKj1JVh7cJBKJyrJnz54p9amM1q1bK+rv378/+vXrhxEjRkAikWD8+PGV3g5V3+s8OJGISFPwQYg14ODBg5gyZQpKSkoQHh6O77//Hps3b8bmzZthampa5qUbbW3tV27vVXdcveoS0JkzZzB27Fg0btwY27ZtU7kzrDzyS1/yS2Evkl/6kl8Kqw5nZ2e0a9cO27dvr/Y2qPL44EQiosrhCFAN2LNnD/T09BATE6M0WlJUVISCgoJa3ffJkycVIz/R0dFljiyVx9n5+RvHz58/r/K+qL/++gvA84civo6nT5/i0aNHr7UNqhw+OJGIqHI4AlQDtLS0IBKJIJMpP6Vm7dq1Km01KSUlBRMmTICFhQViYmIqDD9ZWVm4du2aUpu7uzssLS2RmJiIO3fuKNoLCwuxa9cutG7dGm5ubor2kpISXLt2Dbdv31bazv3798vc56FDh3Dr1i2lW/ip9sgfnPgiPjiRiEgVR4BqwIABA7B//36EhITA398fgiAgOTkZmZmZtTYBNTU1FRMnToRUKsWwYcNw5swZlT7vvPMODA0NFV+XdRu8trY2Fi5ciEmTJmHUqFEICQmBrq4uduzYgQcPHmD9+vVKl+Tu3r0LX19fdOvWDdu2bVO0jx8/HiYmJnB1dUWrVq1QVFSECxcuYP/+/TA1NcVnn31WK+eBlPHBiURElcMAVAN8fX1RVFSE6OhorFixAkZGRvD09MSPP/6IkSNH1so+MzIy8PTpUwBQPGvnZYcOHVIKQK/i5eWFH374AZGRkVi1ahUAwMnJCZs3b0a3bt0qVc+wYcNw4MABxMfH4+HDh9DW1oalpSWCg4MxZswYvgajDvHBiUREFRMJfLgKveFSU1MhkUjg4OBQqUBHr6+oqAjp6ek853WI51w9eN7rXlnn3NHREQBw6dKl197+iy/1Lg/nABEREZHGYQAiIiIijcMARERERBqHAYiIiIg0DgMQERERaRwGICIiItI4DEBERESkcRiAiIiISOMwABEREZHGYQAiIiIijcMARERERBqHAYiIiIg0DgMQERERaRwGICIiItI4DEBERESkcRiAiIiISOMwABEREZHGYQAiIiIijcMARERERBqHAYiIiIg0DgMQERERaRwGICIiItI4DEBERESkcRiAiIiISOMwABEREZHGYQAiIiIijcMARERERBqHAYiIiIg0DgMQERERaRwGICIiItI4DEBERESkcRiAiIiISOO8EQEoPT0dYWFhcHd3h729PVavXq3ukupMQkIC7O3tcerUqXq5/coICQmBt7e32vZPRET0Mh11FyCVSjFt2jRIpVLMmDEDxsbGig/s06dPIywsDCYmJuouk4iIiBoQtY8A3bp1C7du3UJYWBiCg4Ph5+eHDh064PTp01izZg0KCgrUXWK95ufnh4sXL8Ld3V3dpRAREb0x1B6AHjx4AABo3LhxneyvsLCwTvajbvLj1NbWhp6eHrS01P6tJiIiemNU+VNRIpEgMjISvr6+cHFxgZubG959912Eh4fj6dOnin5JSUkICQmBm5sbnJ2d4efnh9jYWAiCoOjj7e2N4OBgAEB4eDjs7e1hb28Pb29vrFmzBgDQr18/RXtV5gZlZ2cr1tm/fz/ef/99dO7cGZMmTVL0SUtLw/Tp0+Hh4QEnJyf069cPERERKC4uVtnexYsXERoaChcXF7i7u2PGjBm4ffs2vL29ERISUuZ+X1bZ+TiFhYX47rvvMHz4cPTo0QNOTk7w9vbGkiVL8PDhwyodZ1n7lJ/Psv68PFcnKysLc+fORa9eveDk5IQ+ffpg0aJFyMvLU6n75s2bmDJlCrp06QJXV1eMHj0aly9fLvdYq0IkEtXYtoiISLNVeQ7QkiVLsGvXLgwePFjxwZ+dnY0jR46gqKgI+vr62LVrFxYsWIBWrVphzJgxMDIyQmJiIpYsWYLLly/jiy++AADMmzcPqampWLduHYKCgtClSxcAgJGREfbs2YMDBw4gPDwcZmZmAJ5/cFfVoUOHEB0djREjRmD48OGKAJaUlIQpU6agZcuWCA4Ohrm5OS5fvowtW7bg3Llz2Lp1K3R0np+eixcvIiQkBLq6uggNDUXz5s1x8uRJBAcHo6ioqMo1VeTu3bvYuXMn3nnnHfj6+kIsFiM1NRU7duzA2bNnER8fD11d3UodZ1mWL1+u0paZmYmoqCiYm5sr2tLT0xESEgJ9fX0EBgbC0tISN27cQFxcHFJSUhAfHw9jY2MAwJ07dzBixAg8fvwYI0aMQLt27XDhwgWEhITA1NT0tc+JWCyGgYHBa2/nTScIAoMeEVEdqHIA+v3339G7d29EREQotc+ZMwcA8PjxYyxduhTm5uaIj49HkyZNAADBwcEYP348du7cCT8/P3Tt2hU+Pj4wNjbGunXr4OLiAj8/P8X20tPTceDAAfj4+MDKyqraB5iRkYGffvoJdnZ2irZnz55h3rx56NChA2JjYyEWixXLevTogenTp2Pfvn3w9/cHACxduhQlJSXYuXOnIoSNGjUKX375JbZt21bt2l6ldevWOHr0qErIcXNzw7/+9S8cPHgQAwcOrPA4X+XF8ww8D1zffvstzM3NsXLlSkV7eHg4GjdujN27dyuFmAEDBmDEiBGIjo7G1KlTAQDffvst8vLyEBkZCR8fHwDAyJEjERUVhZUrV8LS0rJK56AskrQMyIpUR+caCi1DA4g72pa5LD8/HxkZGbC1tVX8QkBERNVX5UtgxsbGyMzMfOWljeTkZBQVFSEkJEQRfgBAR0dHcVnm999/r2a5Vefl5aUSCk6cOIH79+/D398fhYWFyMvLU/xxd3eHgYEBkpOTAQC5ubk4f/48+vTpozICNX78+FqpWSwWK8KPVCpFQUEB8vLy0KNHDwDPR6ReVtZxVkZhYSHGjRuHR48eYd26dWjdujUA4OrVq0hPT8egQYMgk8mUzpGVlRWsra0V50gmk+HgwYOwsbFRhB+5sLAwGBoaVrmussiKiiEUFjXYP68KdwkJCfD19cXkyZPh6+uLhISEGjmfRESarMojQPPnz8enn34KPz8/tGrVCl26dEGvXr0wcOBA6Onp4datWwBQ5oexvC0rK+s1y668Nm3aqLRdu3YNALB48WIsXry4zPXkk7Plx9O2bVuVPs2aNVNcAqppO3fuxI8//oiMjAxIpVKlZS/PAwLKPs6KlJSUYOrUqcjMzMTatWvRqVMnxTL5OVq/fj3Wr19f5vrysJSbm4snT56gffv2Kn309PRgbW2Nx48fV7k+ej7ys2LFCpSUlAB4/j2LiIhA3759ORJERPQaqhyAvL29cfjwYSQnJyue1bNv3z5ERkZix44dtVHjaylr3ohMJgMAzJo1C87OzmWuV91nD5U3f6O0tLRS24iOjsbSpUvh6emJhQsXolmzZhCLxZBKpRg3blyZ83uqOj9GEATMmzcPKSkpWLJkCd5++22V5UD5DzHU09Or0j6p6jIyMhThR04ikSAzM5OPNiAieg3VehCiiYkJfH194evrCwCIi4vDokWLEBsbC1vb53MYMjIy0LdvX6X1MjIyAADW1tYV7qM2J4LKR3P09PTg6elZbl/5KMf169dVlt27d09lZEN+O/+jR49U+stHkyqyZ88eWFpaYtOmTUq3r8tHZWrCN998g71792LixIkICgpSWf7iiFJF56hp06YwMjIqs75nz54hKyuLoxUvSE5Lxe6UYwj06INeHZ3K7WtrawtdXV3k5eUhPz8fZmZmaNq0qeLnjIiIqqdKc4BKS0vL/GB3dHQE8PzSTM+ePWFoaIjY2FilvqWlpfj+++8BAP37969wX/J5I2Xt73X16tUL5ubm2LRpE+7fv6+yXCqVKi4zNW3aFK6urkhKSsKVK1eU+kVFRams26hRI1hYWODkyZNKIzX5+fnYvXt3peqThx75SBXwfEQmMjKyUutXJC4uDlFRURgyZAhmzZpVZh8HBwfY2dkhPj6+zGAjCILiVngtLS3069cPmZmZOHjwoFK/6OjoWrlTrj6LOXIIGbdzEHPkYIV9zczMMGfOHOTn5+Pp06fIz8/H7Nmza+TOOiIiTValEaAnT56gV69e6Nu3LxwcHGBhYYF79+5h165d0NHRweDBg2FsbIx58+ZhwYIFCAwMRGBgIAwMDLB//36cO3cOw4cPR9euXSvcV+fOnQEAERERGDx4MPT09GBra1utib4vMzAwwPLlyxWTSgMCAtCuXTs8efIEWVlZOHDgAD755BMEBAQAeH43VEhICEaNGoWRI0cqboNPS0src2QjNDQUK1euxJgxY+Dj44O8vDzs2rULVlZWirlF5RkwYAAiIiIwZswYvPvuu3j69CkOHDigcimkOq5evYovvvgCpqam6N69O37++Wel5UZGRvDx8YFIJMKKFSsQFhYGf39/+Pv7w87ODlKpFDk5OTh48CD8/f0xbdo0AMDMmTNx7NgxzJw5U+k2+MOHD8Pa2rrSl/80QbHkmdJ/KxIQEIBt27YhOzsbVlZWir+XRERUfVUKQPr6+vjoo49w8uRJnD59GoWFhWjatCk6d+6MsWPHKubTDBs2DM2aNcPGjRsRFRUFqVSKtm3bYsGCBRg1alSl9tWlSxfMnj0b27dvx4IFCyCVSjF16tQaCUAA0LNnTyQkJGDDhg1ITExEbm4uGjVqhFatWiEwMBAeHh6Kvp07d8bWrVuxcuVKREdHQywWw9PTEzExMRg6dCj09fWVtj1mzBg8efIECQkJOH36NNq0aYMZM2YAAP76668KaxszZgwAID4+HsuWLYOpqSn69euHWbNmoVu3bq913Pn5+SgtLcXDhw8xf/58leWWlpaKO7k6dOiAn3/+GVFRUUhKSsLu3bthYGCAFi1aoF+/fkq34ltaWiIuLg4rVqxAQkICBEGAq6srtm7diqVLlyInJ+e16tZ0YrEYJiYmSo9sICKi6hMJ5T0xj8qVl5cHDw8PjBgx4pV3k9HrS01NBQDYPhMgFNb/y2nvfTEfJSUlKC4uhqVFs/8LNSIRRGLdMtd5ceTw0KFDtV5jUVER0tPT4eDgUGOPMaDy8ZyrB8973Xv5nDs6OiIzMxM2Nja4dOnSa29f/pnx4p3NZVH72+Dri6dPn6qM9KxduxYA0Lt3b3WURPXUvXv3kJWVBUEQcPXqVVhbW6NZs2bqLouISG1sbGzKvCGnNtWrAFRaWlrmO6heZmxsrBJWXodUKoWXlxfee+89tG/fHsXFxTh+/DiOHz+O7t27v/I2caKX5T8uUIQfANAWaeFOzm20b20NsZ5epUaAiIgakpoY9amOehWA7ty5g379+lXYb9myZTU6UVRbWxs+Pj5ITk5GQkICpFIpLC0tMWnSJEycOJFvWqdKy8y+BW2RFqRCKXS0tNGmeQsAwCdDAuHetSv0u5b9XKqgoCA8ePBA6V1tRERUffUqAFlYWGDz5s0V9rOxsanR/YpEInz11Vc1uk3STDZWraGtrQ2prBRaWs+fdSXW1YGNVety15PPTeAcBSKimlGvAlBlHlxI9CYzMzbB5MDhWP9TPEwMDSHW1cGM4aNg2qj8V6p8+OGH2LlzJ4YPH15HlRIRNWz1KgARNQRzR4ZiwuChuJaTjfaWVjAzrvi1K15eXvDy8qqD6oiINAMnr1TDqVOnYG9vz7dyU7WZGZuga4eOlQo/RERU8zgCVE89evQIe/fuRVJSEjIzM/HgwQNYWFjAyckJEydORMeOHau0vTNnzmD16tVKz0+YPn16pZ7aDTwPhaGhoWUuMzU1xalTp6pUDxERUW1iAKqnLly4gKVLl6JHjx4YMWIEmjRpgps3b2L79u34/fffsXLlSgwaNKhS2zp27BgmTpyI5s2bY+rUqRCLxdi5cyfCwsKwYcOGKs27CgoKQpcuXZTa+NZ4IiJ60zAA1VPt2rVDYmIi3nrrLaX2IUOGICAgAEuXLsXAgQMrvEW/tLQUCxcuhFgsRkxMDFq1agUAGDp0KAYNGoRFixYhMTGx0rf6u7i4wM/Pr3oHVQEtQwPIKu5Wb2kZGqi7BCIijcEAVANkMhmioqJw/PhxXL9+HQ8fPoSZmRk8PT0xY8YMRaiQs7e3h7+/PwIDA/HNN98gPT0dhoaG8Pf3x8cff4zS0lKsWrUK+/btQ15eHuzs7LBgwQK4uLgotmFlZVVmLXZ2drC1tUVaWhpyc3NhYWGhWJaVlYWSkhK0b99e0fbnn38iJycHAQEBSnUaGxtj2LBhWLNmDc6dO1fpS2EAUFxcDJFIVKMPowQAcUfbGt3emyQ/Px8ZGRmwyctDkyZN1F0OEVGDxwBUA0pKSrBhwwb0798fXl5eMDY2xpUrV7B7926kpKRg7969MDU1VVonLS0Nhw8fRmBgIIYMGYKjR49i48aN0NbWxtWrV1FYWIjRo0ejuLgYmzdvxoQJE3Do0CE0atSo3FpkMhnu378PXV1dmJgoT7D98MMPkZOTgytXrijaLly4AABwdXVV2Za8LTU1tdIB6KuvvkJ4eDgAoEWLFhgyZAgmT54MA4PXG92QSCQoLi5+7e28iRISErBixQqUlJRAV1cXc+bM4RvfiYhqGQNQDRCLxUhOTlb5cPbx8cFHH32E+Ph4jB07VmnZ1atXERcXpwgZH3zwAYYOHYqoqCh4eXlh27ZtEImePyjPxsYGU6ZMwa+//lrhu1JiY2Nx//59+Pv7V2ruzd27dwEAzZs3V1nWosXzpxT/888/FW5HR0cHb7/9Nvr06YOWLVsiLy8PBw8eRFRUFE6cOIGYmJjXDi8N8b29+fn5ivADPA/TERER6Nu3L8zMzNRcHRFRw8UAVANEIpHiw10mk6GwsBBSqRQdOnSAsbExLl68qLKOi4uLyqhL165dkZ6ejtDQUEX4AQB3d3cAwI0bN8qt49SpU/j6669hZWWFuXPnqiw/fPiwSltxcTEA/N8byV8gD1DyPuXp0qUL1q9fr9T2/vvvIyIiAhs2bMC2bdswfvz4CrejaTIyMhThR04ikSAzM1PxfScioprH5wDVkIMHD2LEiBFwdnaGu7s7PDw84OHhgcePH+Phw4cq/Vu3Vn31QePGjctcJm8vaztyZ8+excSJE9GkSRP88MMPKpfcXkUe3CQSicqyZ8+eKfWpjsmTJ0NLSwt//PFHtbfRkNna2kJXV/kFqGKxGLa2DXe+ExHRm4AjQDXg4MGDmDJlCpycnBAeHo6WLVsqJgDPmjWrzEs32trar9zeq+64etUloDNnzmD8+PFo3LgxoqOjVe4MK4/80pf8UtiL5Je+5JfCqsPQ0BBNmzZFXl5etbfRkJmZmWHOnDmIiIiARCKBWCzG7NmzKx1giYioehiAasCePXugp6enMs+lqKgIBQUFtbrvkydPKkZ+oqOjyxxZKo+z8/O3j58/f17lPVN//fUXgOcPRayuwsJCPHjwoEqhTNMEBASgb9++yMzMhI2NDef+EBHVAV4CqwFaWloQiUSQyZSfUrN27VqVtpqUkpKCCRMmwMLCAjExMRWGn6ysLFy7dk2pzd3dHZaWlkhMTMSdO3cU7YWFhdi1axdat24NNzc3RXtJSQmuXbuG27dvK20nPz9fZX+CIGD58uUQBAE+Pj7VOUSNYWZmBnd3d4YfIqI6whGgGjBgwADs378fISEh8Pf3hyAISE5ORmZmZq19oKWmpmLixImQSqUYNmwYzpw5o9LnnXfegaGhoeLrsm6D19bWxsKFCzFp0iSMGjUKISEh0NXVxY4dO/DgwQOsX79e6ZLc3bt34evri27dumHbtm2K9rFjx8Lc3BxOTk5o0aIF8vLycOjQIVy4cAHu7u4YNWpUrZwHIiKi6mAAqgG+vr4oKipCdHQ0VqxYASMjI3h6euLHH3/EyJEja2WfGRkZePr0KQBg5cqVZfY5dOiQUgB6FS8vL/zwww+IjIzEqlWrAABOTk7YvHkzunXrVql63n33Xfzxxx+Ii4tDQUEBdHV10b59e4SHh2PUqFEqE32JiIjUSSQ0xIerUIOSmpoKiUQCBweHSgU6en1FRUVIT0/nOa9DPOfqwfNe92r7nL/4Uu/ycA4QERERaRwGICIiItI4DEBERESkcRiAiIiISOMwABEREZHGYQAiIiIijcPb4OmNd+7cOQiCAF1dXYhEInWXoxEEQUBJSQnPeR3iOVcPnve6V9vnXCKRQCQSKb3FoCx8ECK98eQ/IPzHqe6IRCKIxWJ1l6FReM7Vg+e97tX2OReJRJX6vOAIEBEREWkczgEiIiIijcMARERERBqHAYiIiIg0DgMQERERaRwGICIiItI4DEBERESkcRiAiIiISOMwABEREZHGYQAiIiIijcMARERERBqHAYiIiIg0DgMQERERaRwGICIiItI4DED0RouNjYW3tzc6deqEYcOG4eLFi+ouqUE7c+YMJk6ciF69esHe3h4HDx5Ud0kN3vr16xEYGAhXV1d4eHhg8uTJ+Pvvv9VdVoP3448/YvDgwXBzc4ObmxuCgoJw9OhRdZelUaKiomBvb4+vvvpKLftnAKI31n//+18sW7YMU6ZMwU8//YQOHTpgzJgxyM3NVXdpDVZRURHs7e2xcOFCdZeiMU6fPo1Ro0Zh586d2Lx5M6RSKcaMGYOioiJ1l9agtWjRArNnz0ZCQgJ2796NHj16YMqUKcjIyFB3aRrh4sWL2L59O+zt7dVWg0gQBEFteycqx7Bhw9CpUyd8/vnnAACZTAYvLy+EhIRg/Pjxaq6u4bO3t0dkZCR8fHzUXYpGycvLg4eHB2JiYuDu7q7ucjRKt27dMGfOHAwbNkzdpTRoT548QUBAABYuXIjvv/8eHTp0wPz58+u8Do4A0RtJIpHg0qVL8PT0VLRpaWnB09MT58+fV2NlRLXr8ePHAIDGjRuruRLNUVpail9//RVFRUVwdXVVdzkN3pIlS+Dl5aX077s66Kh170SvkJ+fj9LSUjRt2lSpvWnTppwfQQ2WTCbD0qVL4ebmBjs7O3WX0+BduXIFI0aMwLNnz2BoaIjIyEjY2Niou6wG7ddff0VaWhri4+PVXQoDEBHRm2Lx4sXIyMjAjz/+qO5SNELbtm2xZ88ePH78GPv378dnn32GmJgYhqBacufOHXz11Vf44YcfoKenp+5yGIDozWRmZgZtbW2VCc+5ubkwNzdXU1VEtWfJkiU4cuQIYmJi0KJFC3WXoxHEYjHeeustAICTkxNSU1OxdetWLFmyRM2VNUyXLl1Cbm4uAgICFG2lpaU4c+YMYmNjkZqaCm1t7TqrhwGI3khisRiOjo5ISUlRTMKVyWRISUlBcHCwmqsjqjmCIOCLL77AgQMHsG3bNrRu3VrdJWksmUwGiUSi7jIarB49emDfvn1KbeHh4WjXrh3GjRtXp+EHYACiN9hHH32Ezz77DE5OTnB2dkZ0dDSKi4uVfnugmvXkyRNkZWUpvs7OzkZ6ejoaN26MVq1aqbGyhmvx4sX45ZdfsHbtWhgZGeH+/fsAAGNjY+jr66u5uoZr5cqV6NOnD1q2bIknT57gl19+wenTp7Fp0yZ1l9ZgNWrUSGVum6GhIUxNTdUy540BiN5Yvr6+yMvLw6pVq3D//n04ODhg48aNvARWi/73v/8hNDRU8fWyZcsAAP7+/vj3v/+trrIatLi4OABASEiIUvuyZcsY9mtRbm4uPvvsM9y7dw/Gxsawt7fHpk2b0LNnT3WXRnWEzwEiIiIijcPnABEREZHGYQAiIiIijcMARERERBqHAYiIiIg0DgMQERERaRwGICIiItI4DEBERESkcRiAiIiISOMwABEREZHGYQAiIiIijcMARERERBqHAYiIiIg0DgMQERERaRwGICIiItI4DEBERESkcRiAiIiISOMwABERVYO3tzfmzp2r+PrUqVOwt7fHqVOnamwf9vb2WL16dY1tj4j+j466CyAiqqqEhASEh4crvhaLxWjVqhV69uyJyZMnw9zcXI3VVc3Ro0dx8eJFTJs2Td2lKPnzzz+xbt06XLlyBQ8fPkTTpk3RoUMHDBo0CIMHD1Z3eUSvjQGIiOqt6dOnw8rKChKJBGfPnkVcXByOHj2KX375BQYGBnVai7u7Oy5evAhdXd0qrXf06FHExsaWGYAuXrwIbW3tmiqx0n777TfMmjULDg4OCA0NRePGjZGdnY0zZ85g586dDEDUIDAAEVG91adPH3Tq1AkAMGzYMJiammLz5s04dOgQ3nvvvTLXKSoqgqGhYY3XoqWlBT09vRrdZk1vr7LWrFkDGxsb7NixA2KxWGlZbm5undUhCAKePXsGfX39OtsnaQ7OASKiBqNHjx4AgOzsbADA3Llz4erqiqysLIwbNw6urq6YPXs2AEAmk2HLli0YNGgQOnXqBE9PT3z++ed49OiR0jYFQcDatWvRp08fdO7cGSEhIcjIyFDZ96vmAF24cAHjxo2Du7s7XFxcMHjwYERHRyvqi42NBfB8vo/8j1xZc4DS0tIwduxYuLm5wdXVFWFhYfjrr7+U+iQkJMDe3h5nz57FsmXL0KNHD7i4uGDKlCnIy8ur8DxmZWWhU6dOKuEHAJo2bar0tUwmQ3R0NAYPHoxOnTqhR48eGDNmDFJTUxV9pFIpIiMj4ePjAycnJ3h7e+Obb76BRCJR2pa3tzcmTJiAY8eOISAgAM7Ozti+fTsAoKCgAF999RW8vLzg5OSEd955B1FRUZDJZBUeD1FZOAJERA1GVlYWAMDU1FTRJpVKMWbMGHTp0gWfffaZYjTh888/x08//YSAgACEhIQgOzsbsbGxSEtLQ1xcnOJS1n/+8x98//338PLygpeXFy5duoTRo0ejpKSkwnqOHz+OCRMmoFmzZggNDYW5uTmuXbuGI0eOICwsDEFBQbh37x6OHz+O5cuXV7i9jIwMjBo1CkZGRhg7dix0dHSwY8cOhISEICYmBp07d1bq/+WXX8LExARTp05FTk4OoqOjsWTJEnz33Xfl7qdVq1ZISUnBP//8gxYtWpTbd/78+UhISECfPn3w/vvvo7S0FH/++ScuXLigGJ3717/+hZ9++gnvvvsuPvroI1y8eBHr16/HtWvXEBkZqbS969ev45NPPkFQUBCGDx+Otm3bori4GMHBwbh79y5GjBiBli1b4vz58/jmm29w//59zJ8/v8JzR6RCICKqZ3bv3i3Y2dkJJ06cEHJzc4U7d+4Iv/76q9CtWzfB2dlZ+OeffwRBEITPPvtMsLOzEyIiIpTWP3PmjGBnZyfs3btXqT0pKUmpPTc3V3B0dBTGjx8vyGQyRb9vvvlGsLOzEz777DNF28mTJwU7Ozvh5MmTgiAIglQqFby9vYW+ffsKjx49UtrPi9tavHixYGdnV+Zx2tnZCatWrVJ8PXnyZMHR0VHIyspStN29e1dwdXUVRo0apXJ+PvzwQ6V9LV26VHBwcBAKCgrK3J/crl27BDs7O8HR0VEICQkRvvvuO+HMmTNCaWmpUr+UlBTBzs5O+OKLL1S2Id9venq6YGdnJ8yfP19p+b///W/Bzs5OSElJUbT17dtXsLOzE5KSkpT6RkZGCi4uLsL169eV2iMiIgQHBwfh9u3b5R4PUVl4CYyI6q0PP/wQHh4e8PLywqxZs2BkZIQ1a9agefPmSv0++OADpa8TExNhbGyMnj17Ii8vT/HH0dERhoaGistYJ06cQElJCYKDgyESiRTrh4WFVVhbWloasrOzERoaChMTE6VlL26rskpLS3H8+HH4+PigdevWivZmzZrhvffew9mzZ1FYWKi0zvDhw5X21bVrV5SWliInJ6fcfb3//vvYuHEjunfvjnPnzmHt2rUYNWoU+vfvj3Pnzin6/f777xCJRJg6darKNuT7PXr0KADgo48+Ulo+evRopeVyVlZW6N27t1JbYmIiunTpAhMTE6Xvl6enJ0pLS3HmzJlyj4eoLLwERkT11ueff462bdtCW1sb5ubmaNu2LbS0lH+v09HRUbmMc/PmTTx+/BgeHh5lblc+0ff27dsAgDZt2igtb9KkCRo3blxubbdu3QIA2NnZVfp4ypOXl4fi4mK0bdtWZVn79u0hk8lw584d2NraKtpbtWql1E8exAoKCircX+/evdG7d28UFxfj0qVL+O9//4vt27dj4sSJ+O2339C0aVNkZWWhWbNmSpccX5aTkwMtLS1YW1srtVtYWMDExEQljFlZWals4+bNm7hy5corv1+VmddE9DIGICKqt5ydnRXzTF5FLBarhCKZTIamTZsiIiKizHWaNGlSYzWq08vHLScIQqW3YWBggK5du6Jr164wMzPDmjVrkJSUBH9//yrVUtlRr7Lu+JLJZOjZsyfGjh1b5jovB1SiymAAIiKNY21tjZSUFLi5uZV7i7V8BOXGjRtKl53y8vJU7hZ7mbz/1atX4enp+cp+lQ0GTZo0gYGBAa5fv66y7O+//4aWlhZatmxZqW1Vl5OTEwDg/v37AJ6fx+TkZDx8+PCVo0CWlpaQyWS4efMm2rdvr2h/8OABCgoKYGlpWeF+ra2tUVRUVO55JKoqzgEiIo0zcOBAlJaWYu3atSrLpFKp4hKRp6cndHV1ERMTozRqIr+NvTyOjo6wsrLC1q1bVS45vbgt+QMbK7ospa2tjZ49e+LQoUOK2/yB50Hil19+QZcuXdCoUaMK66qMlJSUMtvl83Xkl+H69+8PQRCwZs0alb7yY/Ty8gKges42b96stLw8AwcOxPnz53Hs2DGVZQUFBZBKpRVug+hlHAEiIo3TrVs3BAUFYf369UhPT0fPnj2hq6uLGzduIDExEfPnz8eAAQPQpEkTjB49GuvXr8eECRPg5eWFtLQ0JCUlwczMrNx9aGlpYdGiRZg0aRKGDh2KgIAAWFhY4O+//0ZmZiY2bdoE4HlQAp7fst6rVy9oa2tj0KBBZW5z5syZOHHiBEaOHImRI0dCW1sbO3bsgEQiwZw5c2rs/EyePBlWVlbo27cvWrdujeLiYpw4cQJ//PEHOnXqhL59+wJ4/twlPz8/bNu2DTdv3kTv3r0hk8lw9uxZdO/eHcHBwejQoQP8/f2xY8cOFBQUwN3dHampqfjpp5/g4+OjeHZTecaMGYPDhw9j4sSJ8Pf3h6OjI4qLi3H16lXs378fhw4dajCXLanuMAARkUZasmQJnJycsH37dnz77bfQ1taGpaUlhgwZAjc3N0W/mTNnQiwWY/v27Th16hScnZ3xww8/YMKECRXuo3fv3oiOjkZkZCR++OEHCIKA1q1bY/jw4Yo+/fv3R0hICH799Vfs3bsXgiC8MgDZ2toiNjYWK1euxPr16yEIApydnbFixQqVZwC9ji+//BKHDh3Cb7/9hnv37inqnjhxIsaNGwcdnf/76Fi2bBns7e0RHx+P5cuXw9jYGE5OTnB1dVXanpWVFX766SccPHgQ5ubmmDBhQpl3j5XFwMAA27Ztw/r165GYmIg9e/agUaNGaNOmDaZNmwZjY+MaO3bSHCKhKrPhiIiIiBoAzgEiIiIijcMARERERBqHAYiIiIg0DgMQERERaRwGICIiItI4DEBERESkcRiAiIiISOMwABEREZHGYQAiIiIijcMARERERBqHAYiIiIg0DgMQERERaZz/B3Lbjmz7DVvMAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 600x450 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["comp.evaluate(side_information='Stemness_Score')\n", "comp.plot_results(side_information='Stemness_Score')"]}, {"cell_type": "markdown", "id": "a5bae82c", "metadata": {}, "source": ["### TSI (Terminal States Identification) score.\n", "Introduction of TSI score can be found [here](https://cellrank.readthedocs.io/en/latest/api/_autosummary/estimators/cellrank.estimators.GPCCA.html#tsi). In short, TSI score evaluate whether the prediction of RegVelo model correspond to given terminal states. Under this mode, `side_key` must be given manually and used to put into `cellrank.estimator.GPCCA.tsi(cluster_key = side_key)`.\n", "\n", "In order to use this mode, `terminal states` and `n_states` are necessary when building ModelComparison object."]}, {"cell_type": "code", "execution_count": 10, "id": "1b1e1001", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "********************************", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "10227ffffc414f38ac91fd4028e73e85", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["WARNING: Unable to import `petsc4py` or `slepc4py`. Using `method='brandts'`\n", "WARNING: For `method='brandts'`, dense matrix is required. Densifying\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Requested more macrostates `9` than available Schur vectors `8`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `10` than available Schur vectors `9`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `11` than available Schur vectors `10`. Recomputing the decomposition\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0ab13f2076f94569839ddb8cfe74b6f4", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8fd457b28af14ebeb3f373bcff29e939", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["WARNING: Unable to import `petsc4py` or `slepc4py`. Using `method='brandts'`\n", "WARNING: For `method='brandts'`, dense matrix is required. Densifying\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Requested more macrostates `9` than available Schur vectors `8`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `10` than available Schur vectors `9`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `11` than available Schur vectors `10`. Recomputing the decomposition\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=11` because it will split complex conjugate eigenvalues. Using `n_states=12`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 22}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "435883084c2947d8a95a85275d8bb1ae", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3480596c15664029aca979a3e71e7b9f", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["WARNING: Unable to import `petsc4py` or `slepc4py`. Using `method='brandts'`\n", "WARNING: For `method='brandts'`, dense matrix is required. Densifying\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Requested more macrostates `9` than available Schur vectors `8`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `10` than available Schur vectors `9`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `11` than available Schur vectors `10`. Recomputing the decomposition\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29, 'dNC_hoxa2b': 26}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0fe6a8d5fdc948319e1f7e57ecde8163", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0a3509b7fb64408bb36e0dffb45e8c0b", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["WARNING: Unable to import `petsc4py` or `slepc4py`. Using `method='brandts'`\n", "WARNING: For `method='brandts'`, dense matrix is required. Densifying\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Requested more macrostates `9` than available Schur vectors `8`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `10` than available Schur vectors `9`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `11` than available Schur vectors `10`. Recomputing the decomposition\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 24}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "50efc42e122a4b6aaaa05856a87c2aa5", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "67031856e09b497aa61bd9ed17ec67a7", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["WARNING: Unable to import `petsc4py` or `slepc4py`. Using `method='brandts'`\n", "WARNING: For `method='brandts'`, dense matrix is required. Densifying\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Requested more macrostates `9` than available Schur vectors `8`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `10` than available Schur vectors `9`. Recomputing the decomposition\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: Requested more macrostates `11` than available Schur vectors `10`. Recomputing the decomposition\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 21}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1dfa69ebf7e3470ea96084fb1f1baf13", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f43f0644b6ca4cec934af6093e84883e", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["WARNING: Unable to import `petsc4py` or `slepc4py`. Using `method='brandts'`\n", "WARNING: For `method='brandts'`, dense matrix is required. Densifying\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Requested more macrostates `9` than available Schur vectors `8`. Recomputing the decomposition\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: Requested more macrostates `10` than available Schur vectors `9`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `11` than available Schur vectors `10`. Recomputing the decomposition\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_hoxa2b_1': 29, 'dNC_hoxa2b_2': 13}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7c3b1a83dfb743139a373d09bd07ec7b", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "08a6558dbf444644ab5431960439f157", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["WARNING: Unable to import `petsc4py` or `slepc4py`. Using `method='brandts'`\n", "WARNING: For `method='brandts'`, dense matrix is required. Densifying\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Requested more macrostates `9` than available Schur vectors `8`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `10` than available Schur vectors `9`. Recomputing the decomposition\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 27}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "72b58b36be564e679ace6e44f6fb27a6", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "91af8f00f6b4400aa9f5d78dbf1ba494", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["WARNING: Unable to import `petsc4py` or `slepc4py`. Using `method='brandts'`\n", "WARNING: For `method='brandts'`, dense matrix is required. Densifying\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Requested more macrostates `9` than available Schur vectors `8`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `10` than available Schur vectors `9`. Recomputing the decomposition\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5f4cf578ea694ee681d87806f5346fe4", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4ce1b4b781f24664aac574216f5e6b28", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["WARNING: Unable to import `petsc4py` or `slepc4py`. Using `method='brandts'`\n", "WARNING: For `method='brandts'`, dense matrix is required. Densifying\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Requested more macrostates `9` than available Schur vectors `8`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `10` than available Schur vectors `9`. Recomputing the decomposition\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: Requested more macrostates `11` than available Schur vectors `10`. Recomputing the decomposition\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'mNC_arch2_1': 29}\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 22}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0c733f52b00f493eaf24b4bcddc1b555", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "313c50271c444552aac060106d9a7ef6", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["WARNING: Unable to import `petsc4py` or `slepc4py`. Using `method='brandts'`\n", "WARNING: For `method='brandts'`, dense matrix is required. Densifying\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Requested more macrostates `9` than available Schur vectors `8`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `10` than available Schur vectors `9`. Recomputing the decomposition\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "896d446e94a1482aa01dae7f050ad714", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "463dbeff17fe4b178133cdcb20699556", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["WARNING: Unable to import `petsc4py` or `slepc4py`. Using `method='brandts'`\n", "WARNING: For `method='brandts'`, dense matrix is required. Densifying\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Requested more macrostates `9` than available Schur vectors `8`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `10` than available Schur vectors `9`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `11` than available Schur vectors `10`. Recomputing the decomposition\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'NPB_nohox': 29}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4992e69a38154c0c865b96204b3e4208", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "47234fc4ff9f4d4fa898075f87668324", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["WARNING: Unable to import `petsc4py` or `slepc4py`. Using `method='brandts'`\n", "WARNING: For `method='brandts'`, dense matrix is required. Densifying\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Requested more macrostates `9` than available Schur vectors `8`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `10` than available Schur vectors `9`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `11` than available Schur vectors `10`. Recomputing the decomposition\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: The following terminal states have different number of cells than requested (30): {'dNC_nohox_2': 8}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "542d31c7558243818cb230bc1fe4e520", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9b04ce15cdfb4e7888ec015673c86364", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["WARNING: Unable to import `petsc4py` or `slepc4py`. Using `method='brandts'`\n", "WARNING: For `method='brandts'`, dense matrix is required. Densifying\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Requested more macrostates `9` than available Schur vectors `8`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `10` than available Schur vectors `9`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `11` than available Schur vectors `10`. Recomputing the decomposition\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cdac48a5ca634229b6901273ccb4dd01", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4ba187d4d66d4649accc018dafd11de3", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["WARNING: Unable to import `petsc4py` or `slepc4py`. Using `method='brandts'`\n", "WARNING: For `method='brandts'`, dense matrix is required. Densifying\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Requested more macrostates `9` than available Schur vectors `8`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `10` than available Schur vectors `9`. Recomputing the decomposition\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Unable to compute macrostates with `n_states=10` because it will split complex conjugate eigenvalues. Using `n_states=11`\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ac98cc377cd14fdeb511535251a052b1", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d956e2cbef7a43919cf6d4da3c232376", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["WARNING: Unable to import `petsc4py` or `slepc4py`. Using `method='brandts'`\n", "WARNING: For `method='brandts'`, dense matrix is required. Densifying\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Requested more macrostates `9` than available Schur vectors `8`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `10` than available Schur vectors `9`. Recomputing the decomposition\n", "WARNING: Requested more macrostates `11` than available Schur vectors `10`. Recomputing the decomposition\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n", "WARNING: Found only one macrostate, making it the singular terminal state\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x450 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["comp.evaluate(side_information='TSI',\n", "              side_key='cell_type')\n", "comp.plot_results(side_information='TSI')"]}, {"cell_type": "markdown", "id": "657775a0", "metadata": {}, "source": ["### CBC(Corss boundary correctness) score\n", "\n", "Introduction of CBC score can be found [here](https://cellrank.readthedocs.io/en/stable/api/developer.html#cellrank.kernels.Kernel.cbc). In short, CBC score evaluate whether the prediction of RegVelo model is reasonable under each given transition event. Under this mode, `side_key` must be given manually and used to put into `cellrank.kernels.kernel.cbc(cluster_key = side_key)`. `side_key` can store either a series of string, or a series of float, because it will be converted into category type in this step. \n", "\n", "In order to use this mode, `state transition` is necessary when building ModelComparison object, and all element must in string format."]}, {"cell_type": "code", "execution_count": 11, "id": "05f0e389", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2b55c04fd5774655805ddcb5c95ccd99", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5d5440a7961542ca87e448476fb62e25", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "64a72d0567ef4fa096ba42c0c345a08e", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2a4b5e2b09d440ecb77e7b564a790318", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b028dab1b9594fa49c991a6528cb9094", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "81886aec9a164a43b5dd5e93c81b0d07", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0b359db37bd04b84b109f953a0aa70ee", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6f147e7a3358417b9b2397abd428d53a", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "39b61759a70f4971bb8a06b7244d7627", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "36409e931bda4ce6bcb38cb65568081f", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "79f07995942245b7919778a173c8bc8f", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6de1f3fa2d9d41158b6e78523e940d6f", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6c64ccf99ae1456ba5b085ffe7e3f61a", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "59a5b1db2ea2424897120a2f1127d447", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1a3b8733d3fd46a7ba0520bc2f93f74e", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "59617705d10a4a9b8623f4b83af7ed44", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a203e7269cd1447680b3f2a91e838dce", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f5cba02bf59148448eae0f16e1dc083c", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8fa6ffacf9574f1bb2a51eeb474f5ce5", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "15c33b24fd4549f2ab644f9e55fd778c", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6587ef94b20a48f780b67ce78d3779c6", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cd01246085264876af11570583129006", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "46e7b30f89be483a9b3aea03751144b0", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3818be6e9ee3477f966ea0d2c2a46210", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1263720e44f143b5aa59a69014436956", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6c723fa3548c4c2e8613ad55ef9906b7", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "82d6cac685174e939122105d13fd4e7f", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1338b7a79c9649289eb1f4f32379fac9", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1ac0ea1134264d2c8a422a4dabaf4609", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0550f7ea6fcb4103aebf75d7412bbce0", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/697 [00:00<?, ?cell/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x450 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["comp.evaluate(side_information='CBC',\n", "              side_key = 'stage_num')\n", "comp.plot_results(side_information='CBC')"]}], "metadata": {"kernelspec": {"display_name": "regvelo-py310", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}