[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Science/Research",
  "Natural Language :: English",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Operating System :: MacOS :: MacOS X",
  "Operating System :: Microsoft :: Windows",
  "Operating System :: POSIX :: Linux",
  "Topic :: Scientific/Engineering :: Bio-Informatics",
]
description = "Estimation of RNA velocity with variational inference."
documentation = "https://scvi-tools.org"
homepage = "https://github.com/theislab/RegVelo/"
license = "BSD-3-Clause"
name = "regvelo"
packages = [
  {include = "regvelo"},
]
readme = "README.md"
version = "0.2.0"

[tool.poetry.dependencies]
anndata = ">=0.10.8"
black = {version = ">=20.8b1", optional = true}
codecov = {version = ">=2.0.8", optional = true}
ruff = {version = "*", optional = true}
importlib-metadata = {version = "^1.0", python = "<3.8"}
ipython = {version = ">=7.1.1", optional = true}
jupyter = {version = ">=1.0", optional = true}
pre-commit = {version = ">=2.7.1", optional = true}
sphinx-book-theme = {version = ">=1.0.0", optional = true}
myst-nb = {version = "*", optional = true}
sphinx-copybutton = {version = "*", optional = true}
sphinxcontrib-bibtex = {version = "2.6.3", optional = true}
ipykernel = {version = "*", optional = true}
pytest = {version = ">=4.4", optional = true}
pytest-cov = {version = "*", optional = true}
python = ">=3.9,<4.0"
python-igraph = {version = "*", optional = true}
scanpy = {version = ">=1.10.3", optional = true}
scanpydoc = {version = ">=0.5", optional = true}
scvelo = ">=0.3.2"
scvi-tools = ">=1.0.0,<1.2.1"
scikit-learn = ">=0.21.2"
velovi = ">=0.3.1"
torchode = ">=0.1.6"
cellrank = ">=2.0.0"
matplotlib = ">=3.7.3"
sphinx = {version = ">=4.1", optional = true}
sphinx-autodoc-typehints = {version = "*", optional = true}
torch = "<2.6.0"


[tool.poetry.extras]
dev = ["black", "pytest", "pytest-cov", "ruff", "codecov", "scanpy", "loompy", "jupyter", "pre-commit"]
docs = [
  "sphinx",
  "scanpydoc",
  "ipython",
  "myst-nb",
  "sphinx-book-theme",
  "sphinx-copybutton",
  "sphinxcontrib-bibtex",
  "ipykernel",
  "ipython",
]
tutorials = ["scanpy"]

[tool.poetry.dev-dependencies]


[tool.coverage.run]
source = ["regvelo"]
omit = [
    "**/test_*.py",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
xfail_strict = true


[tool.black]
include = '\.pyi?$'
exclude = '''
(
  /(
      \.eggs
    | \.git
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
  )/
)
'''

[tool.ruff]
src = ["."]
line-length = 119
target-version = "py38"
select = [
    "F",  # Errors detected by Pyflakes
    "E",  # Error detected by Pycodestyle
    "W",  # Warning detected by Pycodestyle
    "I",  # isort
    "D",  # pydocstyle
    "B",  # flake8-bugbear
    "TID",  # flake8-tidy-imports
    "C4",  # flake8-comprehensions
    "BLE",  # flake8-blind-except
    "UP",  # pyupgrade
    "RUF100",  # Report unused noqa directives
]
ignore = [
    # line too long -> we accept long comment lines; black gets rid of long code lines
    "E501",
    # Do not assign a lambda expression, use a def -> lambda expression assignments are convenient
    "E731",
    # allow I, O, l as variable names -> I is the identity matrix
    "E741",
    # Missing docstring in public package
    "D104",
    # Missing docstring in public module
    "D100",
    # Missing docstring in __init__
    "D107",
    # Errors from function calls in argument defaults. These are fine when the result is immutable.
    "B008",
    # __magic__ methods are are often self-explanatory, allow missing docstrings
    "D105",
    # first line should end with a period [Bug: doesn't work with single-line docstrings]
    "D400",
    # First line should be in imperative mood; try rephrasing
    "D401",
    ## Disable one in each pair of mutually incompatible rules
    # We don’t want a blank line before a class docstring
    "D203",
    # We want docstrings to start immediately after the opening triple quote
    "D213",
    # Missing argument description in the docstring TODO: enable
    "D417",
]

[tool.ruff.per-file-ignores]
"docs/*" = ["I", "BLE001"]
"tests/*" = ["D"]
"*/__init__.py" = ["F401"]
"regvelo/__init__.py" = ["I"]

[tool.jupytext]
formats = "ipynb,md"

[tool.cruft]
skip = [
    "tests",
    "src/**/__init__.py",
    "src/**/basic.py",
    "docs/api.md",
    "docs/changelog.md",
    "docs/references.bib",
    "docs/references.md",
    "docs/notebooks/example.ipynb",
]
