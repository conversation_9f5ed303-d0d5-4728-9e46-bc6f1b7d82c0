name: regvelo_test
channels:
  - pytorch
  - nvidia
  - bioconda
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1
  - _openmp_mutex=4.5
  - absl-py=2.1.0
  - alsa-lib=1.2.13
  - aom=3.9.1
  - arboreto=0.1.6
  - attr=2.5.1
  - aws-c-auth=0.8.0
  - aws-c-cal=0.8.1
  - aws-c-common=0.10.5
  - aws-c-compression=0.3.0
  - aws-c-event-stream=0.5.0
  - aws-c-http=0.9.2
  - aws-c-io=0.15.3
  - aws-c-mqtt=0.11.0
  - aws-c-s3=0.7.5
  - aws-c-sdkutils=0.2.1
  - aws-checksums=0.2.2
  - aws-crt-cpp=0.29.7
  - aws-sdk-cpp=1.11.458
  - azure-core-cpp=1.14.0
  - azure-identity-cpp=1.10.0
  - azure-storage-blobs-cpp=12.13.0
  - azure-storage-common-cpp=12.8.0
  - azure-storage-files-datalake-cpp=12.12.0
  - bedtools=2.31.1
  - binutils=2.43
  - binutils_impl_linux-64=2.43
  - binutils_linux-64=2.43
  - blosc=1.21.6
  - bokeh=3.6.2
  - brotli=1.1.0
  - brotli-bin=1.1.0
  - brotli-python=1.1.0
  - bzip2=1.0.8
  - c-ares=1.34.3
  - c-compiler=1.7.0
  - ca-certificates=2025.1.31
  - cached-property=1.5.2
  - cached_property=1.5.2
  - cccl=2.5.0
  - cellrank=2.0.6
  - certifi=2025.1.31
  - cffi=1.17.1
  - charset-normalizer=3.4.0
  - click=8.1.7
  - cloudpickle=3.1.0
  - colorama=0.4.6
  - cpython=3.10.15
  - cuda-cccl=12.4.127
  - cuda-cccl_linux-64=12.4.127
  - cuda-command-line-tools=12.6.2
  - cuda-compiler=12.6.2
  - cuda-crt-dev_linux-64=12.4.131
  - cuda-crt-tools=12.4.131
  - cuda-cudart=12.4.127
  - cuda-cudart-dev=12.4.127
  - cuda-cudart-dev_linux-64=12.4.127
  - cuda-cudart-static=12.4.127
  - cuda-cudart-static_linux-64=12.4.127
  - cuda-cudart_linux-64=12.4.127
  - cuda-cuobjdump=12.4.127
  - cuda-cupti=12.4.127
  - cuda-cupti-dev=12.4.127
  - cuda-cuxxfilt=12.4.127
  - cuda-documentation=12.4.127
  - cuda-driver-dev=12.4.127
  - cuda-driver-dev_linux-64=12.4.127
  - cuda-gdb=12.4.127
  - cuda-libraries=12.4.1
  - cuda-libraries-dev=12.6.2
  - cuda-libraries-static=12.6.2
  - cuda-nsight=12.4.127
  - cuda-nvcc=12.4.131
  - cuda-nvcc-dev_linux-64=12.4.131
  - cuda-nvcc-impl=12.4.131
  - cuda-nvcc-tools=12.4.131
  - cuda-nvcc_linux-64=12.4.1
  - cuda-nvdisasm=12.4.127
  - cuda-nvml-dev=12.4.127
  - cuda-nvprof=12.4.127
  - cuda-nvprune=12.4.127
  - cuda-nvrtc=12.4.127
  - cuda-nvrtc-dev=12.4.127
  - cuda-nvrtc-static=12.4.127
  - cuda-nvtx=12.4.127
  - cuda-nvvm-dev_linux-64=12.4.131
  - cuda-nvvm-impl=12.4.131
  - cuda-nvvm-tools=12.4.131
  - cuda-nvvp=12.4.127
  - cuda-opencl=12.4.127
  - cuda-opencl-dev=12.4.127
  - cuda-profiler-api=12.4.127
  - cuda-runtime=12.4.1
  - cuda-sanitizer-api=12.4.127
  - cuda-toolkit=12.4.1
  - cuda-tools=12.6.2
  - cuda-version=12.4
  - cuda-visual-tools=12.6.2
  - cxx-compiler=1.7.0
  - cycler=0.12.1
  - cytoolz=1.0.0
  - dav1d=1.2.1
  - dbus=1.13.6
  - docrep=0.3.2
  - et_xmlfile=2.0.0
  - exceptiongroup=1.2.2
  - expat=2.6.4
  - ffmpeg=4.4.0
  - fftw=3.3.10
  - filelock=3.16.1
  - font-ttf-dejavu-sans-mono=2.37
  - font-ttf-inconsolata=3.000
  - font-ttf-source-code-pro=2.038
  - font-ttf-ubuntu=0.83
  - fontconfig=2.15.0
  - fonts-conda-ecosystem=1
  - fonts-conda-forge=1
  - freetype=2.12.1
  - future=1.0.0
  - gcc=12.4.0
  - gcc_impl_linux-64=12.4.0
  - gcc_linux-64=12.4.0
  - gds-tools=1.9.1.3
  - get-annotations=0.1.2
  - gflags=2.2.2
  - giflib=5.2.2
  - glog=0.7.1
  - gmp=6.3.0
  - gmpy2=2.1.5
  - gnutls=3.6.13
  - gxx=12.4.0
  - gxx_impl_linux-64=12.4.0
  - gxx_linux-64=12.4.0
  - h2=4.1.0
  - h5py=3.12.1
  - hdf5=1.14.3
  - hpack=4.0.0
  - htslib=1.21
  - humanize=4.11.0
  - hyperframe=6.0.1
  - hypre=2.31.0
  - idna=3.10
  - importlib-metadata=8.5.0
  - importlib_metadata=8.5.0
  - importlib_resources=6.4.5
  - jinja2=3.1.4
  - joblib=1.4.2
  - kernel-headers_linux-64=3.10.0
  - keyutils=1.6.1
  - kiwisolver=1.4.7
  - krb5=1.21.3
  - lame=3.100
  - lcms2=2.16
  - ld_impl_linux-64=2.43
  - lerc=4.0.0
  - libabseil=20240722.0
  - libaec=1.1.3
  - libarrow=18.1.0
  - libarrow-acero=18.1.0
  - libarrow-dataset=18.1.0
  - libarrow-substrait=18.1.0
  - libavif16=1.1.1
  - libblas=3.9.0
  - libbrotlicommon=1.1.0
  - libbrotlidec=1.1.0
  - libbrotlienc=1.1.0
  - libcap=2.69
  - libcblas=3.9.0
  - libcrc32c=1.1.2
  - libcublas=********
  - libcublas-dev=********
  - libcublas-static=********
  - libcufft=********
  - libcufft-dev=********
  - libcufft-static=********
  - libcufile=1.9.1.3
  - libcufile-dev=1.9.1.3
  - libcufile-static=1.9.1.3
  - libcurand=**********
  - libcurand-dev=**********
  - libcurand-static=**********
  - libcurl=8.10.1
  - libcusolver=********
  - libcusolver-dev=********
  - libcusolver-static=********
  - libcusparse=**********
  - libcusparse-dev=**********
  - libcusparse-static=**********
  - libdeflate=1.22
  - libedit=3.1.20191231
  - libev=4.33
  - libevent=2.1.12
  - libexpat=2.6.4
  - libfabric=1.22.0
  - libffi=3.4.2
  - libgcc=14.1.0
  - libgcc-devel_linux-64=12.4.0
  - libgcc-ng=14.1.0
  - libgcrypt=1.11.0
  - libgcrypt-devel=1.11.0
  - libgcrypt-lib=1.11.0
  - libgcrypt-tools=1.11.0
  - libgfortran=14.1.0
  - libgfortran-ng=14.1.0
  - libgfortran5=14.1.0
  - libglib=2.82.2
  - libgomp=14.1.0
  - libgoogle-cloud=2.31.0
  - libgoogle-cloud-storage=2.31.0
  - libgpg-error=1.51
  - libgrpc=1.67.1
  - libhwloc=2.11.2
  - libiconv=1.17
  - libjpeg-turbo=3.0.0
  - liblapack=3.9.0
  - libllvm14=14.0.6
  - libnghttp2=1.64.0
  - libnl=3.11.0
  - libnpp=12.2.5.30
  - libnpp-dev=12.2.5.30
  - libnpp-static=12.2.5.30
  - libnsl=2.0.1
  - libnvfatbin=12.4.127
  - libnvfatbin-dev=12.4.127
  - libnvfatbin-static=12.4.127
  - libnvjitlink=12.4.127
  - libnvjitlink-dev=12.4.127
  - libnvjitlink-static=12.4.127
  - libnvjpeg=12.3.1.117
  - libnvjpeg-dev=12.3.1.117
  - libnvjpeg-static=12.3.1.117
  - libopenblas=0.3.28
  - libparquet=18.1.0
  - libpng=1.6.44
  - libprotobuf=5.28.2
  - libptscotch=7.0.4
  - libre2-11=2024.07.02
  - libsanitizer=12.4.0
  - libscotch=7.0.4
  - libsqlite=3.46.1
  - libssh2=1.11.1
  - libstdcxx=14.1.0
  - libstdcxx-devel_linux-64=12.4.0
  - libstdcxx-ng=14.1.0
  - libsystemd0=256.7
  - libthrift=0.21.0
  - libtiff=4.7.0
  - libtorch=2.5.1
  - libudev1=256.7
  - libutf8proc=2.9.0
  - libuuid=2.38.1
  - libuv=1.49.2
  - libvpx=1.11.0
  - libwebp=1.4.0
  - libwebp-base=1.4.0
  - libxcb=1.17.0
  - libxcrypt=4.4.36
  - libxkbcommon=1.7.0
  - libxkbfile=1.1.0
  - libxml2=2.13.5
  - libzlib=1.3.1
  - locket=1.0.0
  - lz4=4.3.3
  - lz4-c=1.9.4
  - markdown=3.6
  - markdown-it-py=3.0.0
  - markupsafe=3.0.2
  - mdurl=0.1.2
  - metis=5.1.0
  - mpc=1.3.1
  - mpfr=4.2.1
  - mpi=1.0.1
  - mpi4py=4.0.1
  - mpich=4.2.3
  - mpmath=1.3.0
  - msgpack-python=1.1.0
  - mudata=0.3.1
  - mumps-include=5.7.3
  - mumps-mpi=5.7.3
  - munkres=1.1.4
  - natsort=8.4.0
  - ncurses=6.5
  - nest-asyncio=1.6.0
  - nettle=3.6
  - nomkl=1.0
  - nsight-compute=2024.1.1.4
  - nspr=4.36
  - nss=3.105
  - numba=0.60.0
  - numpy=1.26.4
  - numpy_groupies=0.11.2
  - numpyro=0.15.3
  - ocl-icd=2.3.2
  - openh264=2.1.1
  - openjpeg=2.5.2
  - openpyxl=3.1.5
  - openssl=3.4.1
  - opt-einsum=3.4.0
  - opt_einsum=3.4.0
  - orc=2.0.3
  - pandas=2.2.3
  - parmetis=4.0.3
  - partd=1.4.2
  - pcre2=10.44
  - petsc=3.21.5
  - petsc4py=3.21.5
  - pip=24.2
  - progressbar2=4.5.0
  - protobuf=5.28.2
  - pthread-stubs=0.4
  - pyarrow-core=18.1.0
  - pybind11-abi=4
  - pycparser=2.22
  - pygam=0.9.1
  - pygments=2.18.0
  - pygpcca=1.0.4
  - pynndescent=0.5.13
  - pyro-api=0.1.2
  - pysocks=1.7.1
  - python=3.10.15
  - python-dateutil=2.9.0.post0
  - python-tzdata=2024.2
  - python-utils=3.9.0
  - python_abi=3.10
  - pytorch-cuda=12.4
  - pytorch-lightning=2.4.0
  - pytorch-mutex=1.0
  - pyyaml=6.0.2
  - qhull=2020.2
  - rav1e=0.6.6
  - rdma-core=54.0
  - re2=2024.07.02
  - readline=8.2
  - requests=2.32.3
  - s2n=1.5.9
  - samtools=1.21
  - scalapack=2.2.0
  - scanpy=1.10.3
  - scvi-tools=1.2.0
  - seaborn=0.13.2
  - seaborn-base=0.13.2
  - session-info=1.0.0
  - setuptools=75.1.0
  - six=1.16.0
  - sleef=3.7
  - slepc=3.21.2
  - slepc4py=3.21.2
  - snappy=1.2.1
  - sortedcontainers=2.4.0
  - sparse=0.15.4
  - statsmodels=0.14.4
  - suitesparse=7.8.3
  - superlu=5.2.2
  - superlu_dist=9.1.0
  - svt-av1=2.3.0
  - sysroot_linux-64=2.17
  - tbb=2022.0.0
  - tblib=3.0.0
  - tensorboard=2.18.0
  - threadpoolctl=3.5.0
  - tk=8.6.13
  - toolz=1.0.0
  - tornado=6.4.2
  - typing-extensions=4.12.2
  - typing_extensions=4.12.2
  - tzdata=2024b
  - ucx=1.17.0
  - unicodedata2=15.1.0
  - urllib3=2.2.3
  - wayland=1.23.1
  - wheel=0.44.0
  - x264=1!161.3030
  - x265=3.5
  - xcb-util=0.4.1
  - xcb-util-cursor=0.1.5
  - xcb-util-image=0.4.0
  - xcb-util-keysyms=0.4.1
  - xcb-util-renderutil=0.3.10
  - xcb-util-wm=0.4.2
  - xkeyboard-config=2.43
  - xorg-libice=1.1.1
  - xorg-libsm=1.2.4
  - xorg-libx11=1.8.10
  - xorg-libxau=1.0.11
  - xorg-libxcomposite=0.4.6
  - xorg-libxdamage=1.1.6
  - xorg-libxdmcp=1.1.5
  - xorg-libxext=1.3.6
  - xorg-libxfixes=6.0.1
  - xorg-libxi=1.8.2
  - xorg-libxrandr=1.5.4
  - xorg-libxrender=0.9.11
  - xorg-libxtst=1.2.5
  - xorg-xorgproto=2024.1
  - xyzservices=2024.9.0
  - xz=5.2.6
  - yaml=0.2.5
  - zict=3.0.0
  - zlib=1.3.1
  - zstandard=0.23.0
  - zstd=1.5.6
  - pip:
    - aiohappyeyeballs==2.4.3
    - aiohttp==3.10.10
    - aiosignal==1.3.1
    - alabaster==0.7.16
    - anndata==0.11.0rc2
    - annotated-types==0.7.0
    - anyio==4.6.0
    - appdirs==1.4.4
    - argon2-cffi==23.1.0
    - argon2-cffi-bindings==21.2.0
    - array-api-compat==1.9
    - arrow==1.3.0
    - asciitree==0.3.3
    - asttokens==2.4.1
    - astunparse==1.6.3
    - async-lru==2.0.4
    - async-timeout==4.0.3
    - babel==2.16.0
    - backoff==2.2.1
    - biofluff==3.0.4
    - biopython==1.84
    - biothings-client==0.3.1
    - blessed==1.20.0
    - boltons==24.1.0
    - boto3==1.35.63
    - botocore==1.35.63
    - bucketcache==0.12.1
    - celloracle==0.20.0
    - chex==0.1.87
    - colorcet==3.1.0
    - comm==0.2.2
    - configparser==7.1.0
    - contextlib2==21.6.0
    - contourpy==1.3.0
    - croniter==1.4.1
    - csaps==1.2.0
    - cython==3.0.11
    - dask==2024.2.1
    - dask-expr==0.5.3
    - dateutils==0.6.12
    - debugpy==1.8.7
    - deepdiff==7.0.1
    - diskcache==5.6.3
    - distributed==2024.2.1
    - dm-tree==0.1.8
    - docopt==0.6.2
    - docutils==0.21.2
    - editor==1.6.6
    - etils==1.9.4
    - executing==2.1.0
    - fa2-modified==0.3.10
    - fastapi==0.115.5
    - fasteners==0.19
    - feather-format==0.4.1
    - flatbuffers==24.3.25
    - flax==0.9.0
    - fonttools==4.54.1
    - fqdn==1.5.1
    - frozenlist==1.4.1
    - fsspec==2024.9.0
    - ftpretty==0.4.0
    - gast==0.6.0
    - gdown==5.2.0
    - genomepy==0.16.1
    - gimmemotifs==0.17.2
    - goatools==1.4.12
    - google-pasta==0.2.0
    - grpcio==1.66.2
    - h11==0.14.0
    - hnswlib==0.8.0
    - htseq==2.0.9
    - httpcore==1.0.6
    - httpx==0.27.2
    - igraph==0.11.6
    - imagesize==1.4.1
    - inquirer==3.4.0
    - iprogress==0.4
    - ipykernel==6.29.5
    - ipython==8.28.0
    - ipywidgets==8.1.5
    - isoduration==20.11.0
    - iteround==1.0.4
    - itsdangerous==2.2.0
    - jax==0.4.34
    - jaxlib==0.4.34
    - jedi==0.19.1
    - jmespath==1.0.1
    - joypy==0.2.6
    - json5==0.9.25
    - jsonpickle==4.0.0
    - jsonpointer==3.0.0
    - jupyter==1.1.1
    - jupyter-console==6.6.3
    - jupyter-events==0.10.0
    - jupyter-lsp==2.2.5
    - jupyter-server==2.14.2
    - jupyter-server-terminals==0.5.3
    - jupyterlab==4.2.5
    - jupyterlab-server==2.27.3
    - jupyterlab-widgets==3.0.13
    - keras==3.7.0
    - legacy-api-wrap==1.4
    - leidenalg==0.10.2
    - libclang==18.1.1
    - lightning==2.0.9.post0
    - lightning-cloud==0.5.70
    - lightning-utilities==0.11.7
    - llvmlite==0.43.0
    - logbook==1.8.0
    - logomaker==0.8
    - loguru==0.7.2
    - loompy==3.0.7
    - louvain==0.8.2
    - matplotlib==3.6.3
    - matplotlib-inline==0.1.7
    - ml-collections==0.1.1
    - ml-dtypes==0.4.1
    - mplscience==0.0.7
    - multidict==6.1.0
    - multipledispatch==1.0.0
    - mygene==3.2.2
    - mysql-connector-python==9.1.0
    - namex==0.0.8
    - networkx==3.4.1
    - norns==0.1.6
    - nose==1.3.7
    - notebook==7.2.2
    - notebook-shim==0.2.4
    - numcodecs==0.13.1
    - numdifftools==0.9.41
    - nvidia-cublas-cu12==********
    - nvidia-cuda-cupti-cu12==12.4.127
    - nvidia-cuda-nvrtc-cu12==12.4.127
    - nvidia-cuda-runtime-cu12==12.4.127
    - nvidia-cudnn-cu12==********
    - nvidia-cufft-cu12==********
    - nvidia-curand-cu12==**********
    - nvidia-cusolver-cu12==********
    - nvidia-cusparse-cu12==**********
    - nvidia-nccl-cu12==2.21.5
    - nvidia-nvjitlink-cu12==12.4.127
    - nvidia-nvtx-cu12==12.4.127
    - optax==0.2.3
    - optree==0.13.1
    - orbax-checkpoint==0.7.0
    - ordered-set==4.1.0
    - overrides==7.7.0
    - packaging==24.1
    - parso==0.8.4
    - patsy==0.5.6
    - pexpect==4.9.0
    - pillow==10.4.0
    - platformdirs==4.3.6
    - plotly==5.24.1
    - prometheus-client==0.21.0
    - prompt-toolkit==3.0.48
    - propcache==0.2.0
    - psutil==6.0.0
    - ptyprocess==0.7.0
    - pure-eval==0.2.3
    - pyarrow==18.0.0
    - pybedtools==0.10.0
    - pybigwig==0.3.23
    - pydantic==2.1.1
    - pydantic-core==2.4.0
    - pydot==3.0.2
    - pyfaidx==*******
    - pyjwt==2.10.0
    - pymde==0.1.18
    - pyparsing==3.1.4
    - pyro-ppl==1.9.1
    - pysam==0.22.1
    - python-igraph==0.11.6
    - python-json-logger==2.0.7
    - python-multipart==0.0.17
    - pytz==2024.2
    - pyvis==0.3.2
    - qnorm==0.8.1
    - readchar==4.2.1
    - regvelo==0.1.0
    - represent==2.1
    - rfc3339-validator==0.1.4
    - rfc3986-validator==0.1.1
    - rich==13.9.2
    - rpy2==3.5.17
    - runs==1.2.2
    - s3transfer==0.10.3
    - scib==1.1.5
    - scikit-learn==1.5.2
    - scipy==1.10.1
    - scvelo==0.3.2
    - send2trash==1.8.3
    - sniffio==1.3.1
    - snowballstemmer==2.2.0
    - sphinx==7.3.7
    - sphinx-autodoc-typehints==2.3.0
    - sphinxcontrib-applehelp==2.0.0
    - sphinxcontrib-devhelp==2.0.0
    - sphinxcontrib-htmlhelp==2.1.0
    - sphinxcontrib-jsmath==1.0.1
    - sphinxcontrib-qthelp==2.0.0
    - sphinxcontrib-serializinghtml==2.0.0
    - splicejac==0.0.1
    - stack-data==0.6.3
    - starlette==0.41.2
    - starsessions==1.3.0
    - stdlib-list==0.10.0
    - sympy==1.13.1
    - tenacity==9.0.0
    - tensorboard-data-server==0.7.2
    - tensorflow==2.18.0
    - tensorflow-io-gcs-filesystem==0.37.1
    - tensorstore==0.1.66
    - termcolor==2.5.0
    - terminado==0.18.1
    - texttable==1.7.0
    - tf-keras==2.18.0
    - tomli==2.0.2
    - torch==2.5.1
    - torchaudio==2.5.1
    - torchmetrics==1.4.3
    - torchode==1.0.0
    - torchtyping==0.1.5
    - torchvision==0.20.1
    - tqdm==4.66.5
    - traitlets==5.14.3
    - triton==3.1.0
    - typeguard==2.13.3
    - types-python-dateutil==2.9.0.20241003
    - tzlocal==5.3.1
    - umap-learn==0.5.6
    - unitvelo==*******
    - uri-template==1.3.0
    - uvicorn==0.32.0
    - velocyto==0.17.17
    - velovi==0.3.1
    - wcwidth==0.2.13
    - webcolors==24.8.0
    - websocket-client==1.8.0
    - websockets==12.0
    - werkzeug==3.0.4
    - widgetsnbextension==4.0.13
    - wrapt==1.16.0
    - xarray==2024.9.0
    - xdg==6.0.0
    - xlsxwriter==3.2.0
    - xmod==1.8.1
    - xxhash==3.5.0
    - yarl==1.15.0
    - zarr==2.18.3
    - zipp==3.20.2
prefix: /home/<USER>/weixu.wang/miniconda3/envs/regvelo_test
