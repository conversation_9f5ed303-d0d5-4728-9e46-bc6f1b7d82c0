name: Release

on:
    push:
        tags:
            - "*.*.*"

jobs:
    release:
        name: Release
        runs-on: ubuntu-latest
        steps:
            # will use ref/SHA that triggered it
            - name: Checkout code
              uses: actions/checkout@v3

            - name: Set up Python 3.10
              uses: actions/setup-python@v4
              with:
                  python-version: "3.9"

            - name: Install poetry
              uses: abatilo/actions-poetry@v2.0.0
              with:
                  poetry-version: 1.4.2

            - name: Build project for distribution
              run: poetry build

            - name: Check Version
              id: check-version
              run: |
                  [[ "$(poetry version --short)" =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]] \
                    || echo ::set-output name=prerelease::true

            - name: Publish to PyPI
              env:
                  POETRY_PYPI_TOKEN_PYPI: ${{ secrets.PYPI_TOKEN }}
              run: poetry publish
