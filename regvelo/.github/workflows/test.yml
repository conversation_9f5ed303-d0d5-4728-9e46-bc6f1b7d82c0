# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: regvelo

on:
    push:
        branches: [main]
    pull_request:
        branches: [main]

jobs:
    build:
        runs-on: ubuntu-latest
        strategy:
            matrix:
                python-version: ["3.9", "3.10", "3.11"]

        steps:
            - uses: actions/checkout@v2
            - name: Set up Python ${{ matrix.python-version }}
              uses: actions/setup-python@v2
              with:
                  python-version: ${{ matrix.python-version }}
            - name: Cache pip
              uses: actions/cache@v2
              with:
                  path: ~/.cache/pip
                  key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
                  restore-keys: |
                      ${{ runner.os }}-pip-
            - name: Install dependencies
              run: |
                  pip install pytest-cov
                  pip install .[dev]
            - name: Test with pytest
              run: |
                  pytest --cov-report=xml --cov=velovi
            - name: After success
              run: |
                  bash <(curl -s https://codecov.io/bash)
                  pip list
